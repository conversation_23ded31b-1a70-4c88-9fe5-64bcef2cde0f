from datetime import datetime
from unittest.mock import MagicMock
from uuid import uuid4

import pytest

import app.config
from api.orders.schemas import (
    TrackingInfo,
    UpdateOrderStatusRequest,
    UpdateOrderStatusResponse,
)
from common.ordering import OrderDirection, Ordering
from common.pagination import Pagination
from common.searching import Searching
from mail_delivery.service import AbstractMailService
from orders.adapters.repository import AbstractOrdersRepository
from orders.domain import model
from orders.exceptions import NoOrdersLogs
from orders.services import OrdersService


class TestOrdersService:
    @pytest.fixture
    def mock_repository(self):
        return MagicMock(spec=AbstractOrdersRepository)

    @pytest.fixture
    def mock_mail_service(self):
        return MagicMock(spec=AbstractMailService)

    @pytest.fixture
    def mock_audit_service(self):
        from sim.adapters.externalapi import FakeAuditServiceAPI
        return FakeAuditServiceAPI()

    @pytest.fixture
    def orders_service(self, mock_repository, mock_mail_service, mock_audit_service):
        return OrdersService(
            orders_repository=mock_repository,
            mail_service=mock_mail_service,
            audit_service=mock_audit_service,
        )

    @pytest.fixture(autouse=True)
    def set_bt_manager_mail_id(self, monkeypatch):
        # Remove quotes if present, as settings.BT_MANAGER_MAIL_ID may strip them
        monkeypatch.setenv("BT_MANAGER_MAIL_ID", "<EMAIL>")
        # Also patch settings if imported directly

        app.config.settings.BT_MANAGER_MAIL_ID = "<EMAIL>"

    def test_update_order_status_success(self, orders_service, mock_repository):
        from common.utils import set_trace_id
        from uuid import uuid4 as uuid_gen

        # Set trace ID for the test
        trace_id = uuid_gen()
        set_trace_id(trace_id)

        # Arrange
        order_id = uuid4()
        update_data = UpdateOrderStatusRequest(
            status="CANCELLED", comments="Invalid order"
        )
        expected_response = UpdateOrderStatusResponse(id=order_id)

        # Mock the order with proper status for validation
        mock_order = MagicMock()
        mock_order.status = "PENDING"  # Valid current status for CANCELLED transition
        mock_repository.get_order.return_value = mock_order

        # Mock the final update response
        mock_repository.update_order_status.return_value = expected_response

        # Patch get_order_details to return a valid OrderDetailsResponse for __send_mail

        mock_repository.get_order_details.return_value = model.OrderDetailsResponse(
            id=1,
            order_id=order_id,
            order_by="test_user",
            order_date=datetime(2024, 1, 1, 12, 0, 0),
            status="SHIPPED",
            customer_details=model.OrderCustomer(
                customer_id=uuid4(),
                customer_email="<EMAIL>",
                customer_contact_no="**********",
                customer_account_name="Test Account",
                customer_account_id=1,
            ),
            shipping_details=model.OrderShippingDetails(
                contact_name="John Doe",
                address_line1="123 Test St",
                address_line2="",
                city="Test City",
                state_or_region="Test State",
                postal_code="12345",
                country="Test Country",
            ),
            order_items=[model.OrderItem(sim_type="2FF (Mini) SIMs", quantity=100)],
            order_tracking=None,
        )

        # Act
        result = orders_service.update_order_status(
            order_id=order_id, update_data=update_data, client_ip="127.0.0.1"
        )

        # Assert
        assert result == expected_response
        mock_repository.get_order.assert_called_once_with(order_id)
        mock_repository.add_reject_reason.assert_called_once_with(
            order_id=order_id, update_data=update_data
        )
        mock_repository.update_order_status.assert_called_once_with(
            order_id=order_id, update_data=update_data
        )

    def test_update_order_status_with_tracking(self, orders_service, mock_repository):
        from common.utils import set_trace_id
        from uuid import uuid4 as uuid_gen

        # Set trace ID for the test
        trace_id = uuid_gen()
        set_trace_id(trace_id)

        # Arrange
        order_id = uuid4()

        tracking_info = TrackingInfo(
            reference_id="TRACK123",
            reference_url="https://tracking.example.com/TRACK123",
        )
        update_data = UpdateOrderStatusRequest(
            status="SHIPPED",
            # Only include tracking info for SHIPPED status, no comments
            tracking=tracking_info,
        )
        expected_response = UpdateOrderStatusResponse(id=order_id)

        # Mock the order with proper status for validation
        mock_order = MagicMock()
        mock_order.status = "APPROVED"  # Valid current status for SHIPPED transition
        mock_repository.get_order.return_value = mock_order

        # Mock the final update response
        mock_repository.update_order_status.return_value = expected_response

        # Patch get_order_details to return a valid OrderDetailsResponse for __send_mail
        from datetime import datetime

        from orders.domain import model

        mock_repository.get_order_details.return_value = model.OrderDetailsResponse(
            id=1,
            order_id=order_id,
            order_by="test_user",
            order_date=datetime(2024, 1, 1, 12, 0, 0),
            status="SHIPPED",
            customer_details=model.OrderCustomer(
                customer_id=uuid4(),
                customer_email="<EMAIL>",
                customer_contact_no="**********",
                customer_account_name="Test Account",
                customer_account_id=1,
            ),
            shipping_details=model.OrderShippingDetails(
                contact_name="John Doe",
                address_line1="123 Test St",
                address_line2="",
                city="Test City",
                state_or_region="Test State",
                postal_code="12345",
                country="Test Country",
            ),
            order_items=[model.OrderItem(sim_type="2FF (Mini) SIMs", quantity=100)],
            order_tracking=None,
        )

        # Act
        result = orders_service.update_order_status(
            order_id=order_id, update_data=update_data, client_ip="127.0.0.1"
        )

        # Assert
        assert result == expected_response
        mock_repository.get_order.assert_called_once_with(order_id)
        # For SHIPPED status, tracking information should be added
        mock_repository.add_order_tracking.assert_called_once_with(
            order_id=order_id, tracking=tracking_info
        )
        mock_repository.update_order_status.assert_called_once_with(
            order_id=order_id, update_data=update_data
        )

    def test_update_order_status_repository_error(
        self, orders_service, mock_repository
    ):
        # Arrange
        order_id = uuid4()
        update_data = UpdateOrderStatusRequest(
            status="CANCELLED", comments="cancelled reason"
        )

        # Mock repository get_order to raise error before any validation
        mock_repository.get_order.side_effect = Exception("Database error")

        # Act & Assert
        with pytest.raises(Exception) as exc_info:
            orders_service.update_order_status(
                order_id=order_id, update_data=update_data, client_ip="127.0.0.1"
            )

        assert str(exc_info.value) == "Database error"
        mock_repository.get_order.assert_called_once_with(order_id)
        mock_repository.update_order_status.assert_not_called()
        mock_repository.add_reject_reason.assert_not_called()

    def test_create_order_success(self, orders_service, mock_repository):
        # Arrange
        order_request = model.OrderRequest(
            order_by="test_user",
            customer_details=model.OrderCustomer(
                customer_id="1",
                customer_email="<EMAIL>",
                customer_contact_no="**********",
                customer_account_name="Test Account",
                customer_account_id=1,
            ),
            shipping_details=model.OrderShippingDetails(
                contact_name="John Doe",
                address_line1="123 Test St",
                address_line2="",
                city="Test City",
                state_or_region="Test State",
                postal_code="12345",
                country="Test Country",
            ),
            order_items=[model.OrderItem(sim_type="2FF (Mini) SIMs", quantity=100)],
        )
        expected_response = model.OrderResponse(id=uuid4())
        mock_repository.create_order.return_value = expected_response

        # Patch get_order_details to return a valid OrderDetailsResponse for __send_mail

        mock_repository.get_order_details.return_value = model.OrderDetailsResponse(
            id=1,
            order_id=expected_response.id,
            order_by="test_user",
            order_date=datetime(2024, 1, 1, 12, 0, 0),
            status="PENDING",
            customer_details=model.OrderCustomer(
                customer_id=uuid4(),
                customer_email="<EMAIL>",
                customer_contact_no="**********",
                customer_account_name="Test Account",
                customer_account_id=1,
            ),
            shipping_details=model.OrderShippingDetails(
                contact_name="John Doe",
                address_line1="123 Test St",
                address_line2="",
                city="Test City",
                state_or_region="Test State",
                postal_code="12345",
                country="Test Country",
            ),
            order_items=[model.OrderItem(sim_type="2FF (Mini) SIMs", quantity=100)],
            order_tracking=None,
        )

        # Act
        result = orders_service.create_order(
            order=order_request, user="<EMAIL>"
        )

        # Assert
        assert result == expected_response
        mock_repository.create_order.assert_called_once_with(order=order_request)

    def test_get_orders_success(self, orders_service, mock_repository):
        # Arrange
        expected_orders = [
            model.OrdersData(
                order_id=1,
                order_by="test_user",
                customer_account_name="Test Account",
                order_date="2024-01-01T12:00:00",
                person_placing_order="Test User",
                customer_email="<EMAIL>",
                customer_phone="**********",
                status="PENDING",
                order_item=[],
                customer_account_logo_url="https://example.com/logo.png",
            )
        ]
        expected_count = 1
        mock_repository.get_orders.return_value = expected_orders
        mock_repository.get_orders_count.return_value = expected_count

        # Act
        result_orders, result_count = orders_service.get_orders(
            account_id=None,
            pagination=None,
            ordering=None,
            searching=None,
        )
        # Assert
        assert result_orders == expected_orders
        assert result_count == expected_count
        mock_repository.get_orders.assert_called_once()
        mock_repository.get_orders_count.assert_called_once()

    def test_get_order_details_success(self, orders_service, mock_repository):
        # Arrange
        order_id = uuid4()
        expected_order_details = model.OrderDetailsResponse(
            id=1,
            order_id=order_id,
            order_by="test_user",
            order_date=datetime(2024, 1, 1, 12, 0, 0),
            status="PENDING",
            customer_details=model.OrderCustomer(
                customer_id=uuid4(),
                customer_email="<EMAIL>",
                customer_contact_no="**********",
                customer_account_name="Test Account",
                customer_account_id=1,
            ),
            shipping_details=model.OrderShippingDetails(
                contact_name="John Doe",
                address_line1="123 Test St",
                address_line2="",
                city="Test City",
                state_or_region="Test State",
                postal_code="12345",
                country="Test Country",
            ),
            order_items=[model.OrderItem(sim_type="2FF (Mini) SIMs", quantity=100)],
            order_tracking=None,
        )
        mock_repository.get_order_details.return_value = expected_order_details

        # Act
        result = orders_service.get_order_details(order_id=order_id)

        # Assert
        assert result == expected_order_details
        mock_repository.get_order_details.assert_called_once_with(order_id=order_id)

    def test_get_order_details_repository_error(self, orders_service, mock_repository):
        # Arrange
        order_id = uuid4()
        mock_repository.get_order_details.side_effect = Exception("Database error")

        # Act & Assert
        with pytest.raises(Exception) as exc_info:
            orders_service.get_order_details(order_id=order_id)

        assert str(exc_info.value) == "Database error"
        mock_repository.get_order_details.assert_called_once_with(order_id=order_id)

    def test_get_orders_with_filters_success(self, orders_service, mock_repository):
        # Arrange
        expected_orders = [
            model.OrdersData(
                order_id=uuid4(),
                order_by="test_user",
                customer_account_name="Test Account",
                order_date=datetime(2024, 1, 1, 12, 0, 0),
                person_placing_order="Test User",
                customer_email="<EMAIL>",
                customer_phone="**********",
                status="PENDING",
                order_item=[],
                customer_account_logo_url="https://example.com/logo.png",
            )
        ]
        expected_count = 1
        mock_repository.get_orders.return_value = expected_orders
        mock_repository.get_orders_count.return_value = expected_count

        pagination = Pagination(page=1, page_size=10)
        # Fix Ordering validation - use OrderDirection enum only
        ordering = Ordering(field="order_date", order=OrderDirection.DESC)
        # Fix Searching validation - provide search string and fields set
        searching = Searching(search="test", fields={"customer_email"})

        # Act
        result_orders, result_count = orders_service.get_orders(
            account_id=1,
            pagination=pagination,
            ordering=ordering,
            searching=searching,
        )

        # Assert
        assert result_orders == expected_orders
        assert result_count == expected_count
        mock_repository.get_orders.assert_called_once()
        mock_repository.get_orders_count.assert_called_once()

    def test_get_orders_no_orders_logs_exception(self, orders_service, mock_repository):
        # Arrange
        # Fix expected error message to match actual implementation
        # NoOrdersLogs exception format: "No audit logs found for account {order}."
        test_account = "test_account"
        mock_repository.get_orders.side_effect = NoOrdersLogs(test_account)

        # Act & Assert
        with pytest.raises(NoOrdersLogs) as exc_info:
            orders_service.get_orders(
                account_id=None,
                pagination=None,
                ordering=None,
                searching=None,
            )

        assert str(exc_info.value) == f"No audit logs found for account {test_account}."
        mock_repository.get_orders.assert_called_once()
