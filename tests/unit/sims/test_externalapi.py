import json
from unittest.mock import MagicMock, patch

import pytest

from common.types import IMSI, MSISDN
from sim.adapters.externalapi import SimProvisioning
from sim.domain import model


class DummyQueryResponse:
    def __init__(self, call_id):
        self.call_id = call_id


def make_sim_pod_response():
    return model.SIMPODResponse(
        trid="1234",
        message="success",
        imsi=IMSI("123456789012345"),
        msisdn=MSISDN("9876543210"),
    )


def make_xml_response():
    return {"disconnect-data-session-response": {"@trid": "1234", "#text": "success"}}


@patch("sim.adapters.externalapi.requests.post")
@patch("sim.adapters.externalapi.xmltodict.parse")
def test_pod_sim_success(mock_xmltodict_parse, mock_requests_post):
    sim_prov = SimProvisioning()
    imsi = IMSI("123456789012345")
    msisdn = MSISDN("9876543210")

    sim_prov.query_sim_active_call = MagicMock(
        return_value=DummyQueryResponse(call_id="callid123")
    )
    sim_prov._pod_sim_pip = MagicMock(
        return_value=json.dumps(
            {"request": "<xml></xml>", "header": {"Content-Type": "application/xml"}}
        )
    )
    mock_response = MagicMock()
    mock_response.text = "<xml></xml>"
    mock_requests_post.return_value = mock_response
    mock_xmltodict_parse.return_value = make_xml_response()
    expected_response = make_sim_pod_response()
    sim_prov._parse_pod_sim_response = MagicMock(return_value=expected_response)

    result = sim_prov.pod_sim(imsi, msisdn)
    assert result == expected_response
    sim_prov.query_sim_active_call.assert_called_once_with(imsi=imsi, msisdn=msisdn)
    sim_prov._pod_sim_pip.assert_called_once_with(msisdn, "callid123")
    mock_requests_post.assert_called_once()
    mock_xmltodict_parse.assert_called_once()
    sim_prov._parse_pod_sim_response.assert_called_once()


@patch("sim.adapters.externalapi.requests.post")
@patch("sim.adapters.externalapi.xmltodict.parse")
def test_pod_sim_error_response(mock_xmltodict_parse, mock_requests_post):
    sim_prov = SimProvisioning()
    imsi = IMSI("123456789012345")
    msisdn = MSISDN("9876543210")
    sim_prov.query_sim_active_call = MagicMock(
        return_value=DummyQueryResponse(call_id="callid123")
    )
    sim_prov._pod_sim_pip = MagicMock(
        return_value=json.dumps(
            {"request": "<xml></xml>", "header": {"Content-Type": "application/xml"}}
        )
    )
    mock_response = MagicMock()
    mock_response.text = "<xml></xml>"
    mock_requests_post.return_value = mock_response
    mock_xmltodict_parse.return_value = {
        "disconnect-data-session-error": {"@trid": "1234", "#text": "error"}
    }
    # Simulate error in _parse_pod_sim_response
    sim_prov._parse_pod_sim_response = MagicMock(side_effect=Exception("POD error"))
    with pytest.raises(Exception, match="POD error"):
        sim_prov.pod_sim(imsi, msisdn)


@patch("sim.adapters.externalapi.requests.post")
@patch("sim.adapters.externalapi.xmltodict.parse")
def test_flush_sim_success(mock_xmltodict_parse, mock_requests_post):
    sim_prov = SimProvisioning()
    imsi = IMSI("123456789012345")
    msisdn = MSISDN("9876543210")
    sim_prov._flush_sim_state_pip = MagicMock(
        return_value=json.dumps(
            {"request": "<xml></xml>", "header": {"Content-Type": "application/xml"}}
        )
    )
    mock_response = MagicMock()
    mock_response.text = "<xml></xml>"
    mock_requests_post.return_value = mock_response
    mock_xmltodict_parse.return_value = {
        "flush-sim-state-response": {"@trid": "trid1", "#text": "success"}
    }
    result = sim_prov.flush_sim(imsi, msisdn)
    assert result.trid == "trid1"
    assert result.message == "success"
    assert result.imsi == imsi
    assert result.msisdn == msisdn
    sim_prov._flush_sim_state_pip.assert_called_once_with(msisdn)
    mock_requests_post.assert_called_once()
    mock_xmltodict_parse.assert_called_once()


@patch("sim.adapters.externalapi.requests.post")
@patch("sim.adapters.externalapi.xmltodict.parse")
def test_query_sim_active_call_success(mock_xmltodict_parse, mock_requests_post):
    sim_prov = SimProvisioning()
    imsi = IMSI("123456789012345")
    msisdn = MSISDN("9876543210")
    sim_prov._query_sim_active_call_pip = MagicMock(
        return_value=json.dumps(
            {"request": "<xml></xml>", "header": {"Content-Type": "application/xml"}}
        )
    )
    mock_response = MagicMock()
    mock_response.text = "<xml></xml>"
    mock_requests_post.return_value = mock_response
    mock_xmltodict_parse.return_value = {
        "query-active-call-response": {
            "@trid": "trid2",
            "active-call": {"@id": "callid456"},
            "#text": "success",
        }
    }
    result = sim_prov.query_sim_active_call(imsi, msisdn)
    assert result.trid == "trid2"
    assert result.call_id == "callid456"
    assert result.message == "success"
    assert result.imsi == imsi
    assert result.msisdn == msisdn
    sim_prov._query_sim_active_call_pip.assert_called_once_with(msisdn)
    mock_requests_post.assert_called_once()
    mock_xmltodict_parse.assert_called_once()


@patch("sim.adapters.externalapi.requests.post")
@patch("sim.adapters.externalapi.xmltodict.parse")
def test_sms_sim_ppl_success(mock_xmltodict_parse, mock_requests_post):
    sim_prov = SimProvisioning()
    imsi = IMSI("123456789012345")
    msisdn = MSISDN("9876543210")
    message = "Test message"
    sim_prov._send_sim_sms_pip = MagicMock(
        return_value=json.dumps(
            {"request": "<xml></xml>", "header": {"Content-Type": "application/xml"}}
        )
    )
    mock_response = MagicMock()
    mock_response.text = "<xml></xml>"
    mock_requests_post.return_value = mock_response
    mock_xmltodict_parse.return_value = {
        "send-sms-response": {
            "@trid": "trid3",
            "call-id": "callid789",
            "#text": "success",
        }
    }
    result = sim_prov.sms_sim_ppl(imsi, msisdn, message)
    assert result.trid == "trid3"
    assert result.call_id == "callid789"
    assert result.imsi == imsi
    assert result.msisdn == msisdn
    assert result.message == "success"
    sim_prov._send_sim_sms_pip.assert_called_once_with(msisdn, message)
    mock_requests_post.assert_called_once()
    mock_xmltodict_parse.assert_called_once()
