import unittest
from unittest.mock import MagicMock, create_autospec
from uuid import uuid4

from sqlalchemy.engine import Row
from sqlalchemy.orm import Session

from redis.adapters.repository import DatabaseRepository
from redis.domain import model


class TestDatabaseRepository(unittest.TestCase):
    def setUp(self):
        self.session = MagicMock(spec=Session)
        self.repo = DatabaseRepository(session=self.session)

    def test_get_imsis(self):
        account_id = 123
        mock_imsis = [
            MagicMock(imsi="***************"),
            MagicMock(imsi="***************"),
        ]
        self.session.execute.return_value.all.return_value = mock_imsis

        result = self.repo.get_imsis(account_id)

        self.session.execute.assert_called_once()
        self.assertEqual(result, ["***************", "***************"])

    def test_get_account_name(self):
        account_id = 123
        mock_account_info = create_autospec(Row, instance=True)
        mock_account_info.name = "Test Account"
        self.session.execute.return_value.first.return_value = mock_account_info

        result = self.repo.get_account_name(account_id)

        self.session.execute.assert_called_once()
        self.assertEqual(result, "Test Account")

    def test_get_rules_without_uuid(self):
        account_id = 123
        mock_rules = [
            MagicMock(uuid=uuid4(), status=True, data_volume=100, unit="MB"),
            MagicMock(uuid=uuid4(), status=True, data_volume=200, unit="GB"),
        ]

        mock_actions = [
            MagicMock(
                rules_uuid=mock_rules[0].uuid,
                name="Action1",
                action="ACTION_TYPE",
                action_value="100",
                source=None,
                target=456,
            ),
            MagicMock(
                rules_uuid=mock_rules[1].uuid,
                name="RatePlanChange",
                action="CHANGE_RATE_PLAN",
                action_value="200",
                source=123,
                target=456,
            ),
        ]
        mock_notifications = [
            MagicMock(
                rules_uuid=mock_rules[0].uuid,
                name="Notification1",
                notification=True,
                email="<EMAIL>",
            ),
            MagicMock(
                rules_uuid=mock_rules[1].uuid,
                name="Notification2",
                notification=True,
                email="<EMAIL>",
            ),
        ]

        self.session.execute.return_value.all.side_effect = [
            mock_rules,
            mock_actions,
            mock_notifications,
        ]
        self.session.execute.return_value.fetchall.side_effect = [
            mock_notifications,
        ]

        self.repo.get_account_name = MagicMock(return_value="Test Account")
        self.repo.get_imsis = MagicMock(return_value=["***************"])

        result = self.repo.get_rules(account_id)

        self.session.execute.assert_called()
        self.assertEqual(result.accountName, "Test Account")
        self.assertEqual(len(result.rules), 2)

        first_rule = result.rules[0]
        self.assertEqual(first_rule.simUsageLimit, 100)
        self.assertEqual(first_rule.unit, "MB")
        self.assertEqual(len(first_rule.notifications), 1)

        second_rule = result.rules[1]
        self.assertEqual(second_rule.simUsageLimit, 200)
        self.assertEqual(second_rule.unit, "GB")
        self.assertEqual(len(second_rule.notifications), 1)

        first_action = first_rule.actions[0]
        self.assertEqual(first_action.action, "ACTION_TYPE")
        self.assertEqual(first_action.actionValue, "100")

        second_action = second_rule.actions[0]
        self.assertEqual(second_action.action, "CHANGE_RATE_PLAN")
        self.assertEqual(second_action.source, 123)
        self.assertEqual(second_action.target, 456)

        first_notification = first_rule.notifications[0]
        self.assertEqual(first_notification.notification, True)
        self.assertEqual(first_notification.email, "<EMAIL>")

        second_notification = second_rule.notifications[0]
        self.assertEqual(second_notification.notification, True)
        self.assertEqual(second_notification.email, "<EMAIL>")

    def test_get_rules_with_uuid(self):
        account_id = 123
        rule_uuid = uuid4()
        mock_rule = [MagicMock(uuid=rule_uuid, status=True, data_volume=50, unit="KB")]
        self.session.execute.return_value.all.side_effect = [mock_rule, [], []]
        mock_action1 = MagicMock(
            name="Action1", action="alert", action_value="10GB", rules_uuid=rule_uuid
        )
        mock_action2 = MagicMock(
            name="Action2", action="block", action_value="1TB", rules_uuid=rule_uuid
        )

        self.session.execute().fetchall.return_value = [mock_action1, mock_action2]
        self.repo.get_account_name = MagicMock(return_value="Test Account")
        self.repo.get_imsis = MagicMock(return_value=["***************"])

        result = self.repo.get_rules(account_id, rule_uuid)

        self.session.execute.assert_called()
        self.assertIsInstance(result, model.Rules)
        self.assertEqual(result.simUsageLimit, 50)

    def test_get_rule_status(self):
        account_id = 123
        rule_uuid = uuid4()
        self.session.execute.return_value.scalar.return_value = False

        result = self.repo.get_rule_status(account_id, rule_uuid)

        self.session.execute.assert_called_once()
        self.assertFalse(result)

    def test_get_deleted_rule(self):
        account_id = 123
        rule_uuid = uuid4()
        self.session.execute.return_value.scalar.return_value = True

        result = self.repo.get_deleted_rule(account_id, rule_uuid)

        self.session.execute.assert_called_once()
        self.assertTrue(result)

    def test_get_rules_empty_rule_list(self):
        account_id = 123
        self.session.execute.return_value.all.side_effect = [[], []]
        self.repo.get_account_name = MagicMock(return_value="Test Account")
        self.repo.get_imsis = MagicMock(return_value=["***************"])

        result = self.repo.get_rules(account_id)

        self.assertEqual(result.rules, [])

    def test_get_rules_with_empty_actions_and_notifications(self):
        account_id = 123
        rule_uuid = uuid4()
        mock_rule = [MagicMock(uuid=rule_uuid, status=True, data_volume=100, unit="MB")]
        self.session.execute.return_value.all.side_effect = [mock_rule, [], []]
        self.repo.get_account_name = MagicMock(return_value="Test Account")
        self.repo.get_imsis = MagicMock(return_value=["***************"])

        result = self.repo.get_rules(account_id)

        self.assertEqual(len(result.rules), 1)
        self.assertEqual(result.rules[0].actions, [])
        self.assertEqual(result.rules[0].notifications, [])

    def test_get_rules_with_incomplete_rule_data(self):
        account_id = 123
        mock_rule = [MagicMock(uuid=None, status=True, data_volume=0, unit="")]
        self.session.execute.return_value.all.return_value = mock_rule
        self.repo.get_account_name = MagicMock(return_value="Test Account")
        self.repo.get_imsis = MagicMock(return_value=["***************"])

        result = self.repo.get_rules(account_id)

        self.assertEqual(result.rules, [])

    def test_get_rule_by_uuid(self):
        rule_uuid = uuid4()
        mock_account_id = 123

        mock_row = MagicMock(spec=Row)
        mock_row.account_id = mock_account_id
        self.session.execute.return_value.first.return_value = mock_row

        result = self.repo.get_get_rule_by_uuid(rule_uuid)

        self.session.execute.assert_called_once()
        self.assertEqual(result, mock_account_id)


if __name__ == "__main__":
    unittest.main()
