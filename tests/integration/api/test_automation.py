from unittest.mock import Magic<PERSON>ock
from uuid import UUID

import pytest
from fastapi import BackgroundTasks, HTTPException, status

from api.automation import schemas
from api.automation.endpoints import (
    create_rule,
    create_rule_rely,
    delete_rule,
    get_action,
    get_notification,
    get_rule,
    get_rule_action_enum,
    get_rule_by_uuid,
    get_rule_category,
    get_rule_definition,
    get_rule_rely,
    get_rule_type,
    get_rule_unit_enum,
    update_lock_status_by_rule_uuid,
    update_rule,
    update_rule_rely,
    update_rule_status_by_rule_uuid,
)
from api.automation.examples import RULE_DETAIL_MODEL, RULE_REQUEST, RULE_RESPONSE
from api.resolvers import ResolveError
from auth.exceptions import ForbiddenError
from automation.adapters.exceptions import RuleNotFound
from automation.domain.model import (
    ActionRely,
    ActionValue,
    ChangeRatePlanValue,
    NotificationRely,
    RuleDetailResponseList,
    RuleResponseRely,
    RulesRely,
)
from automation.exceptions import (
    <PERSON><PERSON><PERSON>uleError,
    NoRuleCategory,
    NoRuleDefinition,
    NoRuleTypeFound,
    RuleAlreadyExist,
    RuleDetailsNotFound,
    RuleError,
    RuleRelyError,
    UpdateLockRuleError,
    UpdateRuleError,
)
from automation.services import AbstractAutomationService
from common.constants import MBDUT
from common.ordering import Ordering
from common.pagination import PaginatedResponse, Pagination
from common.searching import Searching
from common.types import (
    DataPercentage,
    DataUnit,
    EnumerationList,
    Service,
    SMSPercentageUnit,
    SMSUnit,
    VoiceMOMTPercentage,
    VoiceMOPercentage,
    VoiceMTPercentage,
    VoiceUnit,
)
from rate_plans.exceptions import RatePlanDoesNotExist
from redis.exception import RedisAPIError


class TestAutomation:
    def setup_method(self):
        self.automation_service = MagicMock(spec=AbstractAutomationService)

    def test_create_rule_success(
        self, automation_service_mock, request_mock, api_security, redis_service_mock
    ):
        mock_auth, mock_request = api_security("/v1/glass/rule")
        mock_request.method = "POST"

        model_response = schemas.RuleDetails(**RULE_RESPONSE)
        expected_response = schemas.CreateRuleDetails(uuids=[model_response])
        create_rule_request = schemas.CreateRuleRequest(**RULE_REQUEST)
        response = create_rule(
            request=mock_request,
            rule_request=create_rule_request,
            background_tasks=BackgroundTasks(),
            redis_service=redis_service_mock,
            automation_service=automation_service_mock,
            authorization=mock_auth,
        )

        assert response == expected_response
        assert isinstance(response, schemas.CreateRuleDetails)

    def test_create_rule_already_exist_error(
        self,
        automation_service_mock_error,
        request_mock,
        api_security,
        redis_service_mock,
    ):
        mock_auth, mock_request = api_security("/v1/glass/rule")
        mock_request.method = "POST"

        create_rule_request = schemas.CreateRuleRequest(**RULE_REQUEST)
        automation_service_mock_data = automation_service_mock_error(
            error_type=RuleError,
            message="An error occoured while creating rule.",
        )

        with pytest.raises(HTTPException) as exc_info:
            create_rule(
                rule_request=create_rule_request,
                request=mock_request,
                background_tasks=BackgroundTasks(),
                redis_service=redis_service_mock,
                automation_service=automation_service_mock_data,
                authorization=mock_auth,
            )
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        "An error occoured while creating rule." in exc_info.value.detail

    def test_create_rule_forbidden_error(
        self,
        automation_service_mock_error,
        request_mock,
        api_security,
        redis_service_mock,
    ):
        mock_auth, mock_request = api_security("/v1/glass/rule")
        mock_request.method = "POST"

        create_rule_request = schemas.RuleRequest(**RULE_REQUEST)
        automation_service_mock_data = automation_service_mock_error(
            error_type=ForbiddenError,
            message="You are trying to access a restricted area.",
        )

        with pytest.raises(HTTPException) as exc_info:
            create_rule(
                rule_request=create_rule_request,
                request=mock_request,
                background_tasks=BackgroundTasks(),
                redis_service=redis_service_mock,
                automation_service=automation_service_mock_data,
                authorization=mock_auth,
            )
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert exc_info.value.detail == "Couldn't process the request."

    def test_create_rule_value_error(
        self,
        automation_service_mock_error,
        request_mock,
        api_security,
        redis_service_mock,
    ):
        mock_auth, mock_request = api_security("/v1/glass/rule")
        mock_request.method = "POST"

        create_rule_request = schemas.CreateRuleRequest(**RULE_REQUEST)
        automation_service_mock_data = automation_service_mock_error(
            error_type=ValueError,
            message="We are unable to process your request.",
        )

        with pytest.raises(HTTPException) as exc_info:
            create_rule(
                rule_request=create_rule_request,
                request=mock_request,
                background_tasks=BackgroundTasks(),
                redis_service=redis_service_mock,
                automation_service=automation_service_mock_data,
                authorization=mock_auth,
            )
        assert exc_info.value.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        assert exc_info.value.detail == "We are unable to process your request."

    def test_create_rule_invalid_rate_plan(
        self,
        automation_service_mock_error,
        request_mock,
        api_security,
        redis_service_mock,
    ):
        mock_auth, mock_request = api_security("/v1/glass/rule")
        mock_request.method = "POST"

        create_rule_request = schemas.CreateRuleRequest(**RULE_REQUEST)
        automation_service_mock_data = automation_service_mock_error(
            error_type=RatePlanDoesNotExist,
            message="Invalid rate plan.",
        )

        with pytest.raises(HTTPException) as exc_info:
            create_rule(
                rule_request=create_rule_request,
                request=mock_request,
                background_tasks=BackgroundTasks(),
                redis_service=redis_service_mock,
                automation_service=automation_service_mock_data,
                authorization=mock_auth,
            )
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert exc_info.value.detail == "Invalid rate plan."

    def test_create_rule_exception(
        self,
        automation_service_mock_error,
        request_mock,
        api_security,
        redis_service_mock,
    ):
        mock_auth, mock_request = api_security("/v1/glass/rule")
        mock_request.method = "POST"

        create_rule_request = schemas.CreateRuleRequest(**RULE_REQUEST)
        automation_service_mock_data = automation_service_mock_error(
            error_type=Exception,
            message="Couldn't process the request.",
        )

        with pytest.raises(HTTPException) as exc_info:
            create_rule(
                rule_request=create_rule_request,
                request=mock_request,
                background_tasks=BackgroundTasks(),
                redis_service=redis_service_mock,
                automation_service=automation_service_mock_data,
                authorization=mock_auth,
            )
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert exc_info.value.detail == "Couldn't process the request."

    @pytest.mark.skip(reason="account_resolver mock is not working need to fix that.")
    def test_get_rule_by_uuid_success(
        self, automation_service_mock, account_resolver_mock, api_security
    ):
        mock_auth, mock_request = api_security("/v1/glass/rule/{rule_uuid}")
        mock_request.method = "GET"

        automation_service_mock.get_rule_by_uuid.return_value = RULE_DETAIL_MODEL
        response = get_rule_by_uuid(
            request=mock_request,
            rule_uuid=UUID("123e4567-e89b-12d3-a456-************"),
            automation_service=automation_service_mock,
            account_resolver=account_resolver_mock,
            authorization=mock_auth,
        )
        assert isinstance(response, schemas.RuleDetail)

    def test_get_rule_by_uuid_not_found(
        self, automation_service_mock_error, account_resolver_mock, api_security
    ):
        mock_auth, mock_request = api_security("/v1/glass/rule/{rule_uuid}")
        mock_request.method = "GET"

        automation_service_mock_data = automation_service_mock_error(
            error_type=RuleNotFound,
            message=UUID("123e4567-e89b-12d3-a456-************"),
        )
        with pytest.raises(HTTPException) as e:
            get_rule_by_uuid(
                request=mock_request,
                rule_uuid=UUID("123e4567-e89b-12d3-a456-************"),
                automation_service=automation_service_mock_data,
                account_resolver=account_resolver_mock,
                authorization=mock_auth,
            )
        assert e.value.status_code == status.HTTP_404_NOT_FOUND
        assert (
            e.value.detail
            == "Rule not found with id : 123e4567-e89b-12d3-a456-************"
        )

    def test_get_rule_by_uuid_forbidden(
        self, automation_service_mock_error, account_resolver_mock, api_security
    ):
        mock_auth, mock_request = api_security("/v1/glass/rule/{rule_uuid}")
        mock_request.method = "GET"

        automation_service_mock_data = automation_service_mock_error(
            error_type=ForbiddenError,
            message="",
        )

        with pytest.raises(HTTPException) as e:
            get_rule_by_uuid(
                request=mock_request,
                rule_uuid=UUID("123e4567-e89b-12d3-a456-************"),
                automation_service=automation_service_mock_data,
                account_resolver=account_resolver_mock,
                authorization=mock_auth,
            )
        assert e.value.status_code == status.HTTP_403_FORBIDDEN
        assert e.value.detail == "You are trying to access a restricted area."

    def test_get_rule_by_uuid_value_error(
        self, automation_service_mock_error, account_resolver_mock, api_security
    ):
        mock_auth, mock_request = api_security("/v1/glass/rule/{rule_uuid}")
        mock_request.method = "GET"

        automation_service_mock_data = automation_service_mock_error(
            error_type=ValueError,
            message="",
        )

        with pytest.raises(HTTPException) as e:
            get_rule_by_uuid(
                request=mock_request,
                rule_uuid=UUID("123e4567-e89b-12d3-a456-************"),
                automation_service=automation_service_mock_data,
                account_resolver=account_resolver_mock,
                authorization=mock_auth,
            )
        assert e.value.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        assert e.value.detail == "We are unable to process your request."

    def test_get_rule_by_uuid_resolver_error(
        self, automation_service_mock, account_resolver_mock, api_security
    ):
        mock_auth, mock_request = api_security("/v1/glass/rule/{rule_uuid}")
        mock_request.method = "GET"

        automation_service_mock.get_rule_by_uuid.return_value = RULE_DETAIL_MODEL
        account_resolver_mock.side_effect = ResolveError

        with pytest.raises(HTTPException) as e:
            get_rule_by_uuid(
                request=mock_request,
                rule_uuid=UUID("123e4567-e89b-12d3-a456-************"),
                automation_service=automation_service_mock,
                account_resolver=account_resolver_mock,
                authorization=mock_auth,
            )
        assert e.value.status_code == status.HTTP_404_NOT_FOUND
        assert e.value.detail == "Account id not found while getting rule details."

    def test_get_rule_by_uuid_general_error(
        self, automation_service_mock_error, account_resolver_mock, api_security
    ):
        mock_auth, mock_request = api_security("/v1/glass/rule/{rule_uuid}")
        mock_request.method = "GET"

        automation_service_mock_data = automation_service_mock_error(
            error_type=Exception,
            message="",
        )

        with pytest.raises(HTTPException) as e:
            get_rule_by_uuid(
                request=mock_request,
                rule_uuid=UUID("123e4567-e89b-12d3-a456-************"),
                automation_service=automation_service_mock_data,
                account_resolver=account_resolver_mock,
                authorization=mock_auth,
            )
        assert e.value.status_code == status.HTTP_400_BAD_REQUEST
        assert e.value.detail == "Couldn't process the request."

    def test_get_rule_type_success(self, make_rule_type_models):
        category_input_data = make_rule_type_models()
        self.automation_service.get_rule_type = MagicMock(
            return_value=[iter(category_input_data), len(category_input_data)]
        )

        pagination = Pagination(page=1, page_size=10)
        ordering = Ordering(field="action", order="ASC")
        searching = Searching(search="search_term", fields=["action"])

        category_data = get_rule_type(
            automation_service=self.automation_service,
            pagination=pagination,
            ordering=ordering,
            searching=searching,
        )
        assert len(category_data.results) == len(category_input_data)
        for result_category, expected_result in zip(
            category_data.results, category_input_data
        ):
            assert isinstance(result_category, schemas.RuleType)
            assert result_category.id == expected_result.id
            assert result_category.rule_type == expected_result.rule_type

    def test_get_name_success(
        self, make_rule_category_models, order_search_pagination_mock
    ):
        name_input_data = make_rule_category_models()
        self.automation_service.get_rule_category = MagicMock(
            return_value=[iter(name_input_data), len(name_input_data)]
        )

        pagination = Pagination(page=1, page_size=10)
        ordering = Ordering(field="action", order="ASC")
        searching = Searching(search="search_term", fields=["action"])

        name_data = get_rule_category(
            automation_service=self.automation_service,
            pagination=pagination,
            ordering=ordering,
            searching=searching,
        )
        assert len(name_data.results) == len(name_input_data)
        for result_name, expected_result in zip(name_data.results, name_input_data):
            assert isinstance(result_name, schemas.RuleCategory)
            assert result_name.id == expected_result.id
            assert result_name.category == expected_result.category

    def test_get_definition_success(
        self, make_rule_definition_models, order_search_pagination_mock
    ):
        definition_input_data = make_rule_definition_models()
        self.automation_service.get_rule_definition = MagicMock(
            return_value=[iter(definition_input_data), len(definition_input_data)]
        )

        pagination = Pagination(page=1, page_size=10)
        ordering = Ordering(field="action", order="ASC")
        searching = Searching(search="search_term", fields=["action"])

        definition_data = get_rule_definition(
            rule_category_id=1,
            automation_service=self.automation_service,
            pagination=pagination,
            ordering=ordering,
            searching=searching,
        )
        assert len(definition_data.results) == len(definition_input_data)
        for result_definition, expected_result in zip(
            definition_data.results, definition_input_data
        ):
            assert isinstance(result_definition, schemas.RuleDefinition)
            assert result_definition.id == expected_result.id
            assert result_definition.definition == expected_result.definition

    def test_get_rule_definition_no_results(self, order_search_pagination_mock):
        mock_ordering, mock_searching, mock_pagination = order_search_pagination_mock
        self.automation_service.get_rule_definition = MagicMock(return_value=([], 0))

        definition_data = get_rule_definition(
            rule_category_id=1,
            automation_service=self.automation_service,
            pagination=mock_pagination,
            ordering=mock_ordering,
            searching=mock_searching,
        )

        assert len(definition_data.results) == 0
        assert definition_data.total_count == 0

    def test_get_rule_definition_not_found(self):
        self.automation_service.get_rule_definition = MagicMock(
            side_effect=NoRuleDefinition(rule_category_id=1)
        )

        with pytest.raises(HTTPException) as exc:
            get_rule_definition(
                rule_category_id=1,
                automation_service=self.automation_service,
                pagination=Pagination(page=1, page_size=10),
                ordering=Ordering(field="definition", order="ASC"),
                searching=None,
            )

        assert exc.value.status_code == 404
        assert exc.value.detail == "No rule definition found for rule_category_id 1."

    def test_get_rule_definition_validation_error(self):
        self.automation_service.get_rule_definition = MagicMock(
            side_effect=ValueError("Invalid value provided")
        )

        with pytest.raises(HTTPException) as exc:
            get_rule_definition(
                rule_category_id=1,
                automation_service=self.automation_service,
                pagination=Pagination(page=1, page_size=10),
                ordering=Ordering(field="definition", order="ASC"),
                searching=None,
            )

        assert exc.value.status_code == 422
        assert exc.value.detail == "We are unable to process your request."

    def test_get_rule_definition_general_exception(self):
        self.automation_service.get_rule_definition = MagicMock(
            side_effect=Exception("An unexpected error occurred")
        )

        with pytest.raises(HTTPException) as exc:
            get_rule_definition(
                rule_category_id=1,
                automation_service=self.automation_service,
                pagination=Pagination(page=1, page_size=10),
                ordering=Ordering(field="definition", order="ASC"),
                searching=None,
            )

        assert exc.value.status_code == 400
        assert exc.value.detail == "Couldn't process the request."

    def test_get_rule_definition_with_searching_and_ordering(
        self, make_rule_definition_models, order_search_pagination_mock
    ):
        mock_ordering, mock_searching, mock_pagination = order_search_pagination_mock
        definition_input_data = make_rule_definition_models()
        self.automation_service.get_rule_definition = MagicMock(
            return_value=(iter(definition_input_data), len(definition_input_data))
        )

        definition_data = get_rule_definition(
            rule_category_id=1,
            automation_service=self.automation_service,
            pagination=mock_pagination,
            ordering=mock_ordering,
            searching=mock_searching,
        )

        assert len(definition_data.results) == len(definition_input_data)
        for result_definition, expected_result in zip(
            definition_data.results, definition_input_data
        ):
            assert isinstance(result_definition, schemas.RuleDefinition)
            assert result_definition.id == expected_result.id
            assert result_definition.definition == expected_result.definition

    def test_get_action_success(self, make_action_models):
        action_input_data = make_action_models()
        self.automation_service.get_actions = MagicMock(
            return_value=iter(action_input_data)
        )
        self.automation_service.get_actions_count = MagicMock(
            return_value=len(action_input_data)
        )
        pagination = Pagination(page=1, page_size=10)
        ordering = Ordering(field="action", order="ASC")
        searching = Searching(search="search_term", fields=["action"])

        action_data = get_action(
            automation_service=self.automation_service,
            pagination=pagination,
            ordering=ordering,
            searching=searching,
        )

        assert len(action_data.results) == len(action_input_data)
        for result_action, expected_action in zip(
            action_data.results, action_input_data
        ):
            assert isinstance(result_action, schemas.Actions)
            assert result_action.id == expected_action.id
            assert result_action.action == expected_action.action

    def test_get_action_no_results(self):
        self.automation_service.get_actions = MagicMock(return_value=iter([]))
        self.automation_service.get_actions_count = MagicMock(return_value=0)
        pagination = Pagination(page=1, page_size=10)
        ordering = Ordering(field="action", order="ASC")
        searching = Searching(search="search_term", fields=["action"])

        with pytest.raises(HTTPException) as exc:
            get_action(
                automation_service=self.automation_service,
                pagination=pagination,
                ordering=ordering,
                searching=searching,
            )
        assert exc.value.status_code == 404
        assert exc.value.detail == "No action found"

    def test_get_notification_success(self, make_notification_models):
        notification_models = make_notification_models

        self.automation_service.get_notification = MagicMock(
            return_value=notification_models
        )
        self.automation_service.get_notification_count = MagicMock(
            return_value=len([notification_models])
        )
        pagination = Pagination(page=1, page_size=10)
        ordering = Ordering(field="notification", order="ASC")
        searching = Searching(search="search_term", fields=["notification"])
        result = get_notification(
            automation_service=self.automation_service,
            pagination=pagination,
            ordering=ordering,
            searching=searching,
        )
        assert len(result.results) == len(notification_models)
        assert result.results[0].id == notification_models[0].id
        assert result.results[0].notification == notification_models[0].notification
        assert result.page == 1
        assert result.page_size == 10

    def test_get_notification_no_results(self):
        self.automation_service.get_notification = MagicMock(return_value=iter([]))
        self.automation_service.get_notification_count = MagicMock(return_value=0)
        pagination = Pagination(page=1, page_size=10)
        ordering = Ordering(field="notification", order="ASC")
        searching = Searching(search="search_term", fields=["notification"])

        with pytest.raises(HTTPException) as exc:
            get_notification(
                automation_service=self.automation_service,
                pagination=pagination,
                ordering=ordering,
                searching=searching,
            )
        assert exc.value.status_code == 404
        assert exc.value.detail == "No notification found"

    def test_update_rule_status_by_rule_uuid_success(
        self, automation_service_mock, api_security, redis_service_mock
    ):
        mock_auth, mock_request = api_security(
            "/v1/glass/rule/{rule_uuid}/{rule_status}"
        )
        mock_request.method = "PATCH"

        response = update_rule_status_by_rule_uuid(
            request=mock_request,
            rule_uuid=UUID("123e4567-e89b-12d3-a456-************"),
            rule_status=True,
            background_tasks=BackgroundTasks(),
            redis_service=redis_service_mock,
            automation_service=automation_service_mock,
            authorization=mock_auth,
        )
        assert isinstance(response, schemas.RuleStatus)

    def test_update_rule_status_by_rule_uuid_rule_not_found(
        self, automation_service_mock_error, api_security, redis_service_mock
    ):
        mock_auth, mock_request = api_security(
            "/v1/glass/rule/{rule_uuid}/{rule_status}"
        )
        mock_request.method = "PATCH"

        automation_service_mock_data = automation_service_mock_error(
            error_type=RuleNotFound,
            message="Rule not found.",
        )

        with pytest.raises(HTTPException) as exc:
            update_rule_status_by_rule_uuid(
                request=mock_request,
                rule_uuid=UUID("123e4567-e89b-12d3-a456-************"),
                rule_status=True,
                background_tasks=BackgroundTasks(),
                redis_service=redis_service_mock,
                automation_service=automation_service_mock_data,
                authorization=mock_auth,
            )
        assert exc.value.status_code == 404
        assert exc.value.detail == "Rule not found."

    def test_update_rule_status_by_rule_uuid_rule_error(
        self, automation_service_mock_error, api_security, redis_service_mock
    ):
        mock_auth, mock_request = api_security(
            "/v1/glass/rule/{rule_uuid}/{rule_status}"
        )
        mock_request.method = "PATCH"

        automation_service_mock_data = automation_service_mock_error(
            error_type=RuleError,
            message="An error occoured while updating rule.",
        )

        with pytest.raises(HTTPException) as exc:
            update_rule_status_by_rule_uuid(
                request=mock_request,
                rule_uuid=UUID("123e4567-e89b-12d3-a456-************"),
                rule_status=True,
                background_tasks=BackgroundTasks(),
                redis_service=redis_service_mock,
                automation_service=automation_service_mock_data,
                authorization=mock_auth,
            )
        assert exc.value.status_code == 400
        assert exc.value.detail == "An error occoured while updating rule."

    def test_update_rule_status_by_rule_uuid_forbidden(
        self, automation_service_mock_error, api_security, redis_service_mock
    ):
        mock_auth, mock_request = api_security(
            "/v1/glass/rule/{rule_uuid}/{rule_status}"
        )
        mock_request.method = "PATCH"

        automation_service_mock_data = automation_service_mock_error(
            error_type=ForbiddenError,
            message="You are trying to access a restricted area.",
        )

        with pytest.raises(HTTPException) as exc:
            update_rule_status_by_rule_uuid(
                request=mock_request,
                rule_uuid=UUID("123e4567-e89b-12d3-a456-************"),
                rule_status=True,
                background_tasks=BackgroundTasks(),
                redis_service=redis_service_mock,
                automation_service=automation_service_mock_data,
                authorization=mock_auth,
            )
        assert exc.value.status_code == 403
        assert exc.value.detail == "You are trying to access a restricted area."

    def test_update_rule_status_by_rule_uuid_value_error(
        self, automation_service_mock_error, api_security, redis_service_mock
    ):
        mock_auth, mock_request = api_security(
            "/v1/glass/rule/{rule_uuid}/{rule_status}"
        )
        mock_request.method = "PATCH"

        automation_service_mock_data = automation_service_mock_error(
            error_type=ValueError,
            message="We are unable to process your request.",
        )

        with pytest.raises(HTTPException) as exc:
            update_rule_status_by_rule_uuid(
                request=mock_request,
                rule_uuid=UUID("123e4567-e89b-12d3-a456-************"),
                rule_status=True,
                background_tasks=BackgroundTasks(),
                redis_service=redis_service_mock,
                automation_service=automation_service_mock_data,
                authorization=mock_auth,
            )
        assert exc.value.status_code == 422
        assert exc.value.detail == "We are unable to process your request."

    def test_update_rule_status_by_rule_uuid_general_error(
        self, automation_service_mock_error, api_security, redis_service_mock
    ):
        mock_auth, mock_request = api_security(
            "/v1/glass/rule/{rule_uuid}/{rule_status}"
        )
        mock_request.method = "PATCH"

        automation_service_mock_data = automation_service_mock_error(
            error_type=Exception,
            message="Couldn't process the request.",
        )

        with pytest.raises(HTTPException) as exc:
            update_rule_status_by_rule_uuid(
                request=mock_request,
                rule_uuid=UUID("123e4567-e89b-12d3-a456-************"),
                rule_status=True,
                background_tasks=BackgroundTasks(),
                redis_service=redis_service_mock,
                automation_service=automation_service_mock_data,
                authorization=mock_auth,
            )
        assert exc.value.status_code == 400
        assert exc.value.detail == "Couldn't process the request."

    def test_update_rule_success(
        self, automation_service_mock, request_mock, api_security, redis_service_mock
    ):
        mock_auth, mock_request = api_security("/v1/glass/rule")
        mock_request.method = "PUT"

        rules_uuid = UUID("456e4567-e89b-12d3-a456-************")
        expected_response = schemas.RuleDetails(**RULE_RESPONSE)
        update_rule_request = schemas.UpdateRuleRequest(**RULE_REQUEST)
        response = update_rule(
            request=mock_request,
            rules_uuid=rules_uuid,
            rule_request=update_rule_request,
            background_tasks=BackgroundTasks(),
            redis_service=redis_service_mock,
            automation_service=automation_service_mock,
            authorization=mock_auth,
        )

        assert response == expected_response
        assert isinstance(response, schemas.RuleDetails)

    def test_update_rule_integrety_error(
        self,
        automation_service_mock_error,
        request_mock,
        api_security,
        redis_service_mock,
    ):
        mock_auth, mock_request = api_security("/v1/glass/rule")
        mock_request.method = "PUT"

        rules_uuid = UUID("456e4567-e89b-12d3-a456-************")

        update_rule_request = schemas.UpdateRuleRequest(**RULE_REQUEST)
        automation_service_mock_data = automation_service_mock_error(
            error_type=RuleError,
            message="An error occoured while creating rule.",
        )

        with pytest.raises(HTTPException) as exc_info:
            update_rule(
                request=mock_request,
                rules_uuid=rules_uuid,
                rule_request=update_rule_request,
                background_tasks=BackgroundTasks(),
                redis_service=redis_service_mock,
                automation_service=automation_service_mock_data,
                authorization=mock_auth,
            )
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        "An error occoured while creating rule." in exc_info.value.detail

    def test_delete_rule_success(
        self, automation_service_mock, redis_service_mock, api_security
    ):
        mock_auth, mock_request = api_security("/v1/glass/rule")
        mock_request.method = "DELETE"

        rules_uuid = UUID("456e4567-e89b-12d3-a456-************")

        response = delete_rule(
            rules_uuid=rules_uuid,
            background_tasks=BackgroundTasks(),
            redis_service=redis_service_mock,
            automation_service=automation_service_mock,
        )

        assert response is None
        automation_service_mock.delete_rule.assert_called_once_with(
            rules_uuid=rules_uuid
        )

    def test_delete_rule_rule_error(self, automation_service_mock, redis_service_mock):
        rules_uuid = UUID("456e4567-e89b-12d3-a456-************")

        automation_service_mock.delete_rule.side_effect = RuleError(
            "Rule deletion error."
        )

        with pytest.raises(HTTPException) as exc_info:
            delete_rule(
                rules_uuid=rules_uuid,
                background_tasks=BackgroundTasks(),
                redis_service=redis_service_mock,
                automation_service=automation_service_mock,
            )

        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert exc_info.value.detail == "An error occoured while deleting rule."
        automation_service_mock.delete_rule.assert_called_once_with(
            rules_uuid=rules_uuid
        )

    def test_delete_rule_rule_not_found(
        self, automation_service_mock, redis_service_mock
    ):
        rules_uuid = UUID("456e4567-e89b-12d3-a456-************")

        automation_service_mock.delete_rule.side_effect = RuleNotFound(
            "Rule not found."
        )

        with pytest.raises(HTTPException) as exc_info:
            delete_rule(
                rules_uuid=rules_uuid,
                background_tasks=BackgroundTasks(),
                redis_service=redis_service_mock,
                automation_service=automation_service_mock,
            )

        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND
        assert exc_info.value.detail == "Rule not found."
        automation_service_mock.delete_rule.assert_called_once_with(
            rules_uuid=rules_uuid
        )

    def test_delete_rule_forbidden_error(
        self, automation_service_mock, redis_service_mock
    ):
        rules_uuid = UUID("456e4567-e89b-12d3-a456-************")

        automation_service_mock.delete_rule.side_effect = ForbiddenError(
            "Forbidden access."
        )

        with pytest.raises(HTTPException) as exc_info:
            delete_rule(
                rules_uuid=rules_uuid,
                background_tasks=BackgroundTasks(),
                redis_service=redis_service_mock,
                automation_service=automation_service_mock,
            )

        assert exc_info.value.status_code == status.HTTP_403_FORBIDDEN
        assert exc_info.value.detail == "You are trying to access a restricted area."
        automation_service_mock.delete_rule.assert_called_once_with(
            rules_uuid=rules_uuid
        )

    def test_delete_rule_generic_error(
        self, automation_service_mock, redis_service_mock
    ):
        rules_uuid = UUID("456e4567-e89b-12d3-a456-************")

        automation_service_mock.delete_rule.side_effect = Exception("Generic error.")

        with pytest.raises(HTTPException) as exc_info:
            delete_rule(
                rules_uuid=rules_uuid,
                background_tasks=BackgroundTasks(),
                redis_service=redis_service_mock,
                automation_service=automation_service_mock,
            )

        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert exc_info.value.detail == "Couldn't process the request."
        automation_service_mock.delete_rule.assert_called_once_with(
            rules_uuid=rules_uuid
        )

    def test_update_rule_forbidden_error(
        self,
        automation_service_mock_error,
        request_mock,
        api_security,
        redis_service_mock,
    ):
        mock_auth, mock_request = api_security("/v1/glass/rule")
        mock_request.method = "PUT"

        rules_uuid = UUID("456e4567-e89b-12d3-a456-************")

        update_rule_request = schemas.UpdateRuleRequest(**RULE_REQUEST)
        automation_service_mock_data = automation_service_mock_error(
            error_type=ForbiddenError,
            message="You are trying to access a restricted area.",
        )

        with pytest.raises(HTTPException) as exc_info:
            update_rule(
                request=mock_request,
                rules_uuid=rules_uuid,
                rule_request=update_rule_request,
                background_tasks=BackgroundTasks(),
                redis_service=redis_service_mock,
                automation_service=automation_service_mock_data,
                authorization=mock_auth,
            )
        assert exc_info.value.status_code == status.HTTP_403_FORBIDDEN
        assert exc_info.value.detail == "You are trying to access a restricted area."

    def test_update_rule_value_error(
        self,
        automation_service_mock_error,
        request_mock,
        api_security,
        redis_service_mock,
    ):
        mock_auth, mock_request = api_security("/v1/glass/rule")
        mock_request.method = "PUT"

        rules_uuid = UUID("456e4567-e89b-12d3-a456-************")

        update_rule_request = schemas.UpdateRuleRequest(**RULE_REQUEST)
        automation_service_mock_data = automation_service_mock_error(
            error_type=ValueError,
            message="We are unable to process your request.",
        )

        with pytest.raises(HTTPException) as exc_info:
            update_rule(
                request=mock_request,
                rules_uuid=rules_uuid,
                rule_request=update_rule_request,
                background_tasks=BackgroundTasks(),
                redis_service=redis_service_mock,
                automation_service=automation_service_mock_data,
                authorization=mock_auth,
            )
        assert exc_info.value.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        assert exc_info.value.detail == "We are unable to process your request."

    def test_update_rule_exception(
        self,
        automation_service_mock_error,
        request_mock,
        api_security,
        redis_service_mock,
    ):
        mock_auth, mock_request = api_security("/v1/glass/rule")
        mock_request.method = "PUT"

        rules_uuid = UUID("456e4567-e89b-12d3-a456-************")

        update_rule_request = schemas.UpdateRuleRequest(**RULE_REQUEST)
        automation_service_mock_data = automation_service_mock_error(
            error_type=Exception,
            message="Couldn't process the request.",
        )

        with pytest.raises(HTTPException) as exc_info:
            update_rule(
                request=mock_request,
                rules_uuid=rules_uuid,
                rule_request=update_rule_request,
                background_tasks=BackgroundTasks(),
                redis_service=redis_service_mock,
                automation_service=automation_service_mock_data,
                authorization=mock_auth,
            )
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert exc_info.value.detail == "Couldn't process the request."

    def test_update_rule_not_found(
        self,
        automation_service_mock_error,
        request_mock,
        api_security,
        redis_service_mock,
    ):
        mock_auth, mock_request = api_security("/v1/glass/rule")
        mock_request.method = "PUT"

        rules_uuid = UUID("456e4567-e89b-12d3-a456-************")

        update_rule_request = schemas.UpdateRuleRequest(**RULE_REQUEST)
        automation_service_mock_data = automation_service_mock_error(
            error_type=RuleNotFound,
            message="Rule not found.",
        )

        with pytest.raises(HTTPException) as exc_info:
            update_rule(
                request=mock_request,
                rules_uuid=rules_uuid,
                rule_request=update_rule_request,
                background_tasks=BackgroundTasks(),
                redis_service=redis_service_mock,
                automation_service=automation_service_mock_data,
                authorization=mock_auth,
            )
        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND
        assert exc_info.value.detail == "Rule not found."

    def test_update_rule_invalid_rate_plan(
        self,
        automation_service_mock_error,
        request_mock,
        api_security,
        redis_service_mock,
    ):
        mock_auth, mock_request = api_security("/v1/glass/rule")
        mock_request.method = "PUT"

        rules_uuid = UUID("456e4567-e89b-12d3-a456-************")

        update_rule_request = schemas.UpdateRuleRequest(**RULE_REQUEST)
        automation_service_mock_data = automation_service_mock_error(
            error_type=RatePlanDoesNotExist,
            message="Invalid rate plan.",
        )

        with pytest.raises(HTTPException) as exc_info:
            update_rule(
                request=mock_request,
                rules_uuid=rules_uuid,
                rule_request=update_rule_request,
                background_tasks=BackgroundTasks(),
                redis_service=redis_service_mock,
                automation_service=automation_service_mock_data,
                authorization=mock_auth,
            )
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert exc_info.value.detail == "Invalid rate plan."

    def test_get_rule_category_no_records(self, automation_service_mock):
        automation_service_mock.get_rule_category.return_value = ([], 0)

        pagination = Pagination(page=1, page_size=10)
        ordering = Ordering(field="action", order="ASC")
        searching = Searching(search="search_term", fields=["action"])

        response = get_rule_category(
            automation_service=automation_service_mock,
            pagination=pagination,
            ordering=ordering,
            searching=searching,
        )

        assert isinstance(response, PaginatedResponse)
        assert len(response.results) == 0
        assert response.total_count == 0

    def test_get_rule_category_not_found(self):
        self.automation_service.get_rule_category.side_effect = NoRuleCategory()

        pagination = Pagination(page=1, page_size=10)
        ordering = Ordering(field="action", order="ASC")
        searching = Searching(search="search_term", fields=["action"])

        with pytest.raises(HTTPException) as exc:
            get_rule_category(
                automation_service=self.automation_service,
                pagination=pagination,
                ordering=ordering,
                searching=searching,
            )
        assert exc.value.status_code == status.HTTP_404_NOT_FOUND
        assert exc.value.detail == "No rule category found"

    def test_get_rule_category_value_error(self, automation_service_mock_error):

        automation_service_mock_data = automation_service_mock_error(
            error_type=ValueError,
            message="Invalid input.",
        )
        pagination = Pagination(page=1, page_size=10)
        ordering = Ordering(field="action", order="ASC")
        searching = Searching(search="search_term", fields=["action"])

        with pytest.raises(HTTPException) as exc:
            get_rule_category(
                automation_service=automation_service_mock_data,
                pagination=pagination,
                ordering=ordering,
                searching=searching,
            )
        assert exc.value.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        assert exc.value.detail == "We are unable to process your request."

    def test_get_rule_category_general_exception(self, automation_service_mock_error):
        automation_service_mock_data = automation_service_mock_error(
            error_type=Exception,
            message="Unexpected error.",
        )
        pagination = Pagination(page=1, page_size=10)
        ordering = Ordering(field="action", order="ASC")
        searching = Searching(search="search_term", fields=["action"])

        with pytest.raises(HTTPException) as exc:
            get_rule_category(
                automation_service=automation_service_mock_data,
                pagination=pagination,
                ordering=ordering,
                searching=searching,
            )
        assert exc.value.status_code == status.HTTP_400_BAD_REQUEST
        assert exc.value.detail == "Couldn't process the request."

    def test_create_rule_ERROR_already_exist(
        self,
        request_mock,
        api_security,
        redis_service_mock,
    ):
        mock_auth, mock_request = api_security("/v1/glass/rule")
        mock_request.method = "POST"

        create_rule_request = schemas.CreateRuleRequest(**RULE_REQUEST)
        self.automation_service.create_rule.side_effect = RuleAlreadyExist()

        with pytest.raises(HTTPException) as exc_info:
            create_rule(
                rule_request=create_rule_request,
                request=mock_request,
                background_tasks=BackgroundTasks(),
                redis_service=redis_service_mock,
                automation_service=self.automation_service,
                authorization=mock_auth,
            )
        assert exc_info.value.status_code == status.HTTP_409_CONFLICT
        assert "Rule already exist." in exc_info.value.detail

    def test_create_rule_ERROR_create_rule_error(
        self,
        automation_service_mock_error,
        request_mock,
        api_security,
        redis_service_mock,
    ):
        mock_auth, mock_request = api_security("/v1/glass/rule")
        mock_request.method = "POST"

        create_rule_request = schemas.CreateRuleRequest(**RULE_REQUEST)
        automation_service_mock_data = automation_service_mock_error(
            error_type=CreateRuleError,
            message="Create Rule error.",
        )

        with pytest.raises(HTTPException) as exc_info:
            create_rule(
                rule_request=create_rule_request,
                request=mock_request,
                background_tasks=BackgroundTasks(),
                redis_service=redis_service_mock,
                automation_service=automation_service_mock_data,
                authorization=mock_auth,
            )
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert "An error occoured while creating rule." in exc_info.value.detail

    def test_create_rule_ERROR_forbidden_error(
        self,
        automation_service_mock_error,
        request_mock,
        api_security,
        redis_service_mock,
    ):
        mock_auth, mock_request = api_security("/v1/glass/rule")
        mock_request.method = "POST"

        create_rule_request = schemas.CreateRuleRequest(**RULE_REQUEST)
        automation_service_mock_data = automation_service_mock_error(
            error_type=ForbiddenError,
            message="Forbidden.",
        )

        with pytest.raises(HTTPException) as exc_info:
            create_rule(
                rule_request=create_rule_request,
                request=mock_request,
                background_tasks=BackgroundTasks(),
                redis_service=redis_service_mock,
                automation_service=automation_service_mock_data,
                authorization=mock_auth,
            )
        assert exc_info.value.status_code == status.HTTP_403_FORBIDDEN
        assert "You are trying to access a restricted area." in exc_info.value.detail

    def test_create_rule_ERROR_redis_api_error(
        self,
        request_mock,
        api_security,
        redis_service_mock,
    ):
        mock_auth, mock_request = api_security("/v1/glass/rule")
        mock_request.method = "POST"

        create_rule_request = schemas.CreateRuleRequest(**RULE_REQUEST)
        self.automation_service.create_rule.side_effect = RedisAPIError()

        with pytest.raises(HTTPException) as exc_info:
            create_rule(
                rule_request=create_rule_request,
                request=mock_request,
                background_tasks=BackgroundTasks(),
                redis_service=redis_service_mock,
                automation_service=self.automation_service,
                authorization=mock_auth,
            )
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert "An error occoured while creating rule." in exc_info.value.detail

    def test_update_rule_already_exist(
        self,
        request_mock,
        api_security,
        redis_service_mock,
    ):
        mock_auth, mock_request = api_security("/v1/glass/rule")
        mock_request.method = "PUT"

        rules_uuid = UUID("456e4567-e89b-12d3-a456-************")

        update_rule_request = schemas.UpdateRuleRequest(**RULE_REQUEST)
        self.automation_service.update_rule.side_effect = RuleAlreadyExist()

        with pytest.raises(HTTPException) as exc_info:
            update_rule(
                request=mock_request,
                rules_uuid=rules_uuid,
                rule_request=update_rule_request,
                background_tasks=BackgroundTasks(),
                redis_service=redis_service_mock,
                automation_service=self.automation_service,
                authorization=mock_auth,
            )
        assert exc_info.value.status_code == status.HTTP_409_CONFLICT
        assert exc_info.value.detail == "Rule already exist."

    def test_update_rule_error(
        self,
        automation_service_mock_error,
        request_mock,
        api_security,
        redis_service_mock,
    ):
        mock_auth, mock_request = api_security("/v1/glass/rule")
        mock_request.method = "PUT"

        rules_uuid = UUID("456e4567-e89b-12d3-a456-************")

        update_rule_request = schemas.UpdateRuleRequest(**RULE_REQUEST)
        automation_service_mock_data = automation_service_mock_error(
            error_type=UpdateRuleError,
            message="Update Rule Error.",
        )

        with pytest.raises(HTTPException) as exc_info:
            update_rule(
                request=mock_request,
                rules_uuid=rules_uuid,
                rule_request=update_rule_request,
                background_tasks=BackgroundTasks(),
                redis_service=redis_service_mock,
                automation_service=automation_service_mock_data,
                authorization=mock_auth,
            )
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert exc_info.value.detail == "Update Rule Error."

    def test_update_rule_redis_api_error(
        self,
        request_mock,
        api_security,
        redis_service_mock,
    ):
        mock_auth, mock_request = api_security("/v1/glass/rule")
        mock_request.method = "PUT"

        rules_uuid = UUID("456e4567-e89b-12d3-a456-************")

        update_rule_request = schemas.UpdateRuleRequest(**RULE_REQUEST)
        self.automation_service.update_rule.side_effect = RedisAPIError()

        with pytest.raises(HTTPException) as exc_info:
            update_rule(
                request=mock_request,
                rules_uuid=rules_uuid,
                rule_request=update_rule_request,
                background_tasks=BackgroundTasks(),
                redis_service=redis_service_mock,
                automation_service=self.automation_service,
                authorization=mock_auth,
            )
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert exc_info.value.detail == "An error occoured while updating rule."

    def test_get_rule_type_no_rule_type_error(self, make_rule_type_models):
        category_input_data = make_rule_type_models()
        self.automation_service.get_rule_type = MagicMock(
            return_value=[iter(category_input_data), len(category_input_data)]
        )

        pagination = Pagination(page=1, page_size=10)
        ordering = Ordering(field="action", order="ASC")
        searching = Searching(search="search_term", fields=["action"])
        self.automation_service.get_rule_type.side_effect = NoRuleTypeFound()

        with pytest.raises(HTTPException) as exc_info:
            get_rule_type(
                automation_service=self.automation_service,
                pagination=pagination,
                ordering=ordering,
                searching=searching,
            )

        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND
        assert exc_info.value.detail == "No Type found."

    def test_get_rule_type_no_Exception_error(self, make_rule_type_models):
        category_input_data = make_rule_type_models()
        self.automation_service.get_rule_type = MagicMock(
            return_value=[iter(category_input_data), len(category_input_data)]
        )

        pagination = Pagination(page=1, page_size=10)
        ordering = Ordering(field="action", order="ASC")
        searching = Searching(search="search_term", fields=["action"])
        self.automation_service.get_rule_type.side_effect = Exception()

        with pytest.raises(HTTPException) as exc_info:
            get_rule_type(
                automation_service=self.automation_service,
                pagination=pagination,
                ordering=ordering,
                searching=searching,
            )

        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert exc_info.value.detail == "Couldn't process the request."

    def test_get_rule_not_found(
        self,
        request_mock,
        api_security,
        automation_service_mock_error,
        account_resolver_mock,
        order_search_pagination_mock,
    ):
        mock_ordering, mock_searching, mock_pagination = order_search_pagination_mock
        mock_auth, mock_request = api_security("/v1/glass/rule")
        mock_request.method = "GET"
        automation_service_mock_data = automation_service_mock_error(
            error_type=RuleDetailsNotFound,
            message="No rules found.",
        )
        with pytest.raises(HTTPException) as exc_info:
            get_rule(
                request=mock_request,
                automation_service=automation_service_mock_data,
                account_resolver=account_resolver_mock,
                ordering=mock_ordering,
                searching=mock_searching,
                pagination=mock_pagination,
                authorization=mock_auth,
            )
        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND
        assert exc_info.value.detail == "No rules found."

    def test_get_rule_forbidden(
        self,
        request_mock,
        api_security,
        automation_service_mock_error,
        account_resolver_mock,
        order_search_pagination_mock,
    ):
        mock_ordering, mock_searching, mock_pagination = order_search_pagination_mock
        mock_auth, mock_request = api_security("/v1/glass/rule")
        mock_request.method = "GET"
        automation_service_mock_data = automation_service_mock_error(
            error_type=ForbiddenError,
            message="You are trying to access a restricted area.",
        )
        with pytest.raises(HTTPException) as exc_info:
            get_rule(
                request=mock_request,
                automation_service=automation_service_mock_data,
                account_resolver=account_resolver_mock,
                ordering=mock_ordering,
                searching=mock_searching,
                pagination=mock_pagination,
                authorization=mock_auth,
            )
        assert exc_info.value.status_code == status.HTTP_403_FORBIDDEN
        assert exc_info.value.detail == "You are trying to access a restricted area."

    def test_get_rule_value_error(
        self,
        request_mock,
        api_security,
        automation_service_mock_error,
        account_resolver_mock,
        order_search_pagination_mock,
    ):
        mock_ordering, mock_searching, mock_pagination = order_search_pagination_mock
        mock_auth, mock_request = api_security("/v1/glass/rule")
        mock_request.method = "GET"
        automation_service_mock_data = automation_service_mock_error(
            error_type=ValueError,
            message="Invalid input.",
        )
        with pytest.raises(HTTPException) as exc_info:
            get_rule(
                request=mock_request,
                automation_service=automation_service_mock_data,
                account_resolver=account_resolver_mock,
                ordering=mock_ordering,
                searching=mock_searching,
                pagination=mock_pagination,
                authorization=mock_auth,
            )
        assert exc_info.value.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        assert exc_info.value.detail == "We are unable to process your request."

    def test_get_rule_resolver_error(
        self,
        request_mock,
        api_security,
        automation_service_mock,
        account_resolver_mock,
        order_search_pagination_mock,
    ):
        mock_ordering, mock_searching, mock_pagination = order_search_pagination_mock
        mock_auth, mock_request = api_security("/v1/glass/rule")
        mock_request.method = "GET"
        automation_service_mock.get_rule.return_value = (
            RuleDetailResponseList(result=[RULE_DETAIL_MODEL]),
            1,
        )
        account_resolver_mock.side_effect = ResolveError
        with pytest.raises(HTTPException) as exc_info:
            get_rule(
                request=mock_request,
                automation_service=automation_service_mock,
                account_resolver=account_resolver_mock,
                ordering=mock_ordering,
                searching=mock_searching,
                pagination=mock_pagination,
                authorization=mock_auth,
            )
        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND
        assert (
            exc_info.value.detail == "Account id not found while getting rule details."
        )

    def test_get_rule_general_exception(
        self,
        request_mock,
        api_security,
        automation_service_mock_error,
        account_resolver_mock,
        order_search_pagination_mock,
    ):
        mock_ordering, mock_searching, mock_pagination = order_search_pagination_mock
        mock_auth, mock_request = api_security("/v1/glass/rule")
        mock_request.method = "GET"
        automation_service_mock_data = automation_service_mock_error(
            error_type=Exception,
            message="Unexpected error.",
        )
        with pytest.raises(HTTPException) as exc_info:
            get_rule(
                request=mock_request,
                automation_service=automation_service_mock_data,
                account_resolver=account_resolver_mock,
                ordering=mock_ordering,
                searching=mock_searching,
                pagination=mock_pagination,
                authorization=mock_auth,
            )
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert exc_info.value.detail == "Couldn't process the request."

    def test_get_rule_success(
        request_mock,
        api_security,
        automation_service_mock,
        account_resolver_mock,
        order_search_pagination_mock,
    ):
        mock_ordering, mock_searching, mock_pagination = order_search_pagination_mock
        mock_auth, mock_request = api_security("/v1/glass/rule")
        mock_request.method = "GET"
        RULE_DETAIL_MODEL.rule_category = "test"
        RULE_DETAIL_MODEL.rule_definition = "test"
        RULE_DETAIL_MODEL.rule_type = "test"
        automation_service_mock.get_rule.return_value = (
            RuleDetailResponseList(result=[RULE_DETAIL_MODEL]),
            1,
        )

        mock_account = MagicMock()
        mock_account.name = "Account-1"
        mock_account.id = 1
        mock_account.logo_url = "https://test.com"
        mock_account.logo_key = "mock_logo_key"

        account_resolver_mock.side_effect = lambda account_ids: {
            account_id: mock_account for account_id in account_ids
        }.__getitem__

        response = get_rule(
            request=mock_request,
            automation_service=automation_service_mock,
            account_resolver=account_resolver_mock,
            ordering=mock_ordering,
            searching=mock_searching,
            pagination=mock_pagination,
            authorization=mock_auth,
        )

        assert isinstance(response.results[0], schemas.RulesData)
        assert response.results[0].uuid == UUID("bdb5365e-40c1-4902-b8d2-55a13a72d3ea")
        assert response.results[0].rule_name == RULE_DETAIL_MODEL.rule_name
        assert response.results[0].rule_type == RULE_DETAIL_MODEL.rule_type
        assert response.results[0].rule_category == RULE_DETAIL_MODEL.rule_category
        assert response.results[0].rule_definition == RULE_DETAIL_MODEL.rule_definition

    def test_update_rule_lock_error(
        self,
        automation_service_mock_error,
        request_mock,
        api_security,
        redis_service_mock,
    ):
        mock_auth, mock_request = api_security("/v1/glass/rule")
        mock_request.method = "PUT"

        rules_uuid = UUID("456e4567-e89b-12d3-a456-************")

        update_rule_request = schemas.UpdateRuleRequest(**RULE_REQUEST)
        automation_service_mock_data = automation_service_mock_error(
            error_type=UpdateRuleError,
            message="Rule is locked",
        )

        with pytest.raises(HTTPException) as exc_info:
            update_rule(
                request=mock_request,
                rules_uuid=rules_uuid,
                rule_request=update_rule_request,
                background_tasks=BackgroundTasks(),
                redis_service=redis_service_mock,
                automation_service=automation_service_mock_data,
                authorization=mock_auth,
            )
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert exc_info.value.detail == "Rule is locked"

    def test_update_rule_locked_error(
        self,
        automation_service_mock_error,
        request_mock,
        api_security,
        redis_service_mock,
    ):
        mock_auth, mock_request = api_security("/v1/glass/rule")
        mock_request.method = "PUT"

        rules_uuid = UUID("456e4567-e89b-12d3-a456-************")

        update_rule_request = schemas.UpdateRuleRequest(**RULE_REQUEST)
        automation_service_mock_data = automation_service_mock_error(
            error_type=UpdateLockRuleError,
            message="This rule has been locked by BT Administrator. You can’t edit.",
        )

        with pytest.raises(HTTPException) as exc_info:
            update_rule(
                request=mock_request,
                rules_uuid=rules_uuid,
                rule_request=update_rule_request,
                background_tasks=BackgroundTasks(),
                redis_service=redis_service_mock,
                automation_service=automation_service_mock_data,
                authorization=mock_auth,
            )
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert (
            exc_info.value.detail
            == "This rule has been locked by BT Administrator. You can’t edit."
        )

    def test_update_rule_lock_by_rule_uuid_success(
        self, automation_service_mock, api_security, redis_service_mock
    ):
        mock_auth, mock_request = api_security(
            "/v1/glass/rule/lock/{rule_uuid}/{lock_status}"
        )
        mock_request.method = "PATCH"

        automation_service_mock.update_lock_status_by_rule_uuid.return_value = (
            schemas.LockStatus(
                uuid=UUID("123e4567-e89b-12d3-a456-************"),
                lock=True,
            )
        )

        response = update_lock_status_by_rule_uuid(
            request=mock_request,
            rule_uuid=UUID("123e4567-e89b-12d3-a456-************"),
            lock_status=True,
            background_tasks=BackgroundTasks(),
            redis_service=redis_service_mock,
            automation_service=automation_service_mock,
            authorization=mock_auth,
        )
        assert isinstance(response, schemas.LockStatus)

    def test_update_rule_lock_by_rule_uuid_rule_not_found(
        self, automation_service_mock_error, api_security, redis_service_mock
    ):
        mock_auth, mock_request = api_security(
            "/v1/glass/rule/lock/{rule_uuid}/{lock_status}"
        )
        mock_request.method = "PATCH"

        automation_service_mock_data = automation_service_mock_error(
            error_type=RuleNotFound,
            message="Rule not found.",
        )

        automation_service_mock_data.update_lock_status_by_rule_uuid.side_effect = (
            RuleNotFound("Rule not found.")
        )

        with pytest.raises(HTTPException) as exc:
            update_lock_status_by_rule_uuid(
                request=mock_request,
                rule_uuid=UUID("123e4567-e89b-12d3-a456-************"),
                lock_status=True,
                background_tasks=BackgroundTasks(),
                redis_service=redis_service_mock,
                automation_service=automation_service_mock_data,
                authorization=mock_auth,
            )
        assert exc.value.status_code == 404
        assert exc.value.detail == "Rule not found."

    def test_update_rule_lock_by_rule_uuid_rule_error(
        self, automation_service_mock_error, api_security, redis_service_mock
    ):
        mock_auth, mock_request = api_security(
            "/v1/glass/rule/lock/{rule_uuid}/{lock_status}"
        )
        mock_request.method = "PATCH"

        automation_service_mock_data = automation_service_mock_error(
            error_type=RuleError,
            message="An error occoured while updating rule.",
        )

        automation_service_mock_data.update_lock_status_by_rule_uuid.side_effect = (
            RuleError("An error occoured while updating rule.")
        )

        with pytest.raises(HTTPException) as exc:
            update_lock_status_by_rule_uuid(
                request=mock_request,
                rule_uuid=UUID("123e4567-e89b-12d3-a456-************"),
                lock_status=True,
                background_tasks=BackgroundTasks(),
                redis_service=redis_service_mock,
                automation_service=automation_service_mock_data,
                authorization=mock_auth,
            )
        assert exc.value.status_code == 400
        assert exc.value.detail == "An error occoured while updating rule."

    def test_update_rule_lock_forbidden_error(
        self, automation_service_mock_error, api_security, redis_service_mock
    ):
        mock_auth, mock_request = api_security(
            "/v1/glass/rule/lock/{rule_uuid}/{lock_status}"
        )
        mock_request.method = "PATCH"

        automation_service_mock_data = automation_service_mock_error(
            error_type=ForbiddenError,
            message="Access to restricted area.",
        )

        automation_service_mock_data.update_lock_status_by_rule_uuid.side_effect = (
            ForbiddenError("Access to restricted area.")
        )

        with pytest.raises(HTTPException) as exc:
            update_lock_status_by_rule_uuid(
                request=mock_request,
                rule_uuid=UUID("123e4567-e89b-12d3-a456-************"),
                lock_status=True,
                background_tasks=BackgroundTasks(),
                redis_service=redis_service_mock,
                automation_service=automation_service_mock_data,
                authorization=mock_auth,
            )
        assert exc.value.status_code == 403
        assert exc.value.detail == "You are trying to access a restricted area."

    def test_update_rule_lock_value_error(
        self, automation_service_mock_error, api_security, redis_service_mock
    ):
        mock_auth, mock_request = api_security(
            "/v1/glass/rule/lock/{rule_uuid}/{lock_status}"
        )
        mock_request.method = "PATCH"

        automation_service_mock_data = automation_service_mock_error(
            error_type=ValueError,
            message="Unable to process the request.",
        )

        automation_service_mock_data.update_lock_status_by_rule_uuid.side_effect = (
            ValueError("Unable to process the request.")
        )

        with pytest.raises(HTTPException) as exc:
            update_lock_status_by_rule_uuid(
                request=mock_request,
                rule_uuid=UUID("123e4567-e89b-12d3-a456-************"),
                lock_status=True,
                background_tasks=BackgroundTasks(),
                redis_service=redis_service_mock,
                automation_service=automation_service_mock_data,
                authorization=mock_auth,
            )
        assert exc.value.status_code == 422
        assert exc.value.detail == "We are unable to process your request."

    def test_generic_exception_handling(
        self, automation_service_mock_error, api_security, redis_service_mock
    ):
        mock_auth, mock_request = api_security(
            "/v1/glass/rule/lock/{rule_uuid}/{lock_status}"
        )
        mock_request.method = "PATCH"

        automation_service_mock_data = automation_service_mock_error(
            error_type=Exception,
            message="A general error occurred while processing.",
        )

        automation_service_mock_data.update_lock_status_by_rule_uuid.side_effect = (
            Exception("A general error occurred while processing.")
        )

        with pytest.raises(HTTPException) as exc:
            update_lock_status_by_rule_uuid(
                request=mock_request,
                rule_uuid=UUID("123e4567-e89b-12d3-a456-************"),
                lock_status=True,
                background_tasks=BackgroundTasks(),
                redis_service=redis_service_mock,
                automation_service=automation_service_mock_data,
                authorization=mock_auth,
            )

        assert exc.value.status_code == 400
        assert exc.value.detail == "Couldn't process the request."

    def test_get_rule_rely_success(self):
        rule_definition_id = 1
        mock_response = RuleResponseRely(
            id=1,
            rules=RulesRely(
                data_volume=True,
                data_unit=False,
                sms_unit=False,
                voice_unit=True,
                threshold=False,
                data_percentage=False,
                voice_mo_percentage=True,
                voice_mt_percentage=False,
                voice_momt_percentage=False,
                sms_percentage=False,
            ),
            action=ActionRely(
                view_deactivate_sim=True,
                required_deactivate_sim=False,
                view_rate_plan_change=True,
                required_rate_plan_change=False,
                add_any_rate_plan=True,
                is_monthly_pool=True,
                data=True,
                voice_mo=True,
                voice_mt=False,
                sms=False,
            ),
            notification=NotificationRely(
                view_email=True, view_sms=False, view_push=False
            ),
        )

        self.automation_service.get_rule_rely = MagicMock(return_value=mock_response)
        response = get_rule_rely(
            rule_definition_id=rule_definition_id,
            automation_service=self.automation_service,
        )

        assert isinstance(response, schemas.RuleResponseRely)

    def test_get_rule_rely_rule_rely_error(self):
        rule_definition_id = 1
        self.automation_service.get_rule_rely.side_effect = RuleRelyError(
            f"Rely with rule_definition_id={rule_definition_id} not exists."
        )

        with pytest.raises(HTTPException) as exc:
            get_rule_rely(
                rule_definition_id=rule_definition_id,
                automation_service=self.automation_service,
            )
        assert exc.value.status_code == 404
        assert (
            exc.value.detail
            == f"Rely with rule_definition_id={rule_definition_id} not exists."
        )

    def test_get_rule_rely_value_error(self):
        rule_definition_id = 1
        self.automation_service.get_rule_rely.side_effect = ValueError(
            "We are unable to process your request."
        )
        with pytest.raises(HTTPException) as exc:
            get_rule_rely(
                rule_definition_id=rule_definition_id,
                automation_service=self.automation_service,
            )
        assert exc.value.status_code == 422
        assert exc.value.detail == "We are unable to process your request."

    def test_get_rule_rely_generic_exception(self):
        rule_definition_id = 1
        self.automation_service.get_rule_rely.side_effect = Exception(
            "Couldn't process the request."
        )
        with pytest.raises(HTTPException) as exc:
            get_rule_rely(
                rule_definition_id=rule_definition_id,
                automation_service=self.automation_service,
            )
        assert exc.value.status_code == 400
        assert exc.value.detail == "Couldn't process the request."

    def test_create_rule_rely_success(self):
        rule_request = schemas.RuleRequestRely(
            rule_definition_id=1,
            data_volume=True,
            data_unit=False,
            sms_unit=False,
            voice_unit=True,
            threshold=False,
            data_percentage=False,
            view_deactivate_sim=True,
            required_deactivate_sim=False,
            view_rate_plan_change=True,
            required_rate_plan_change=False,
            add_any_rate_plan=True,
            is_monthly_pool=True,
            view_email=True,
            view_sms=False,
            view_push=False,
            voice_mo_percentage=True,
            voice_mt_percentage=False,
            voice_momt_percentage=False,
            sms_percentage=False,
            data=True,
            voice_mo=True,
            voice_mt=False,
            sms=False,
        )

        self.automation_service.create_rule_rely = MagicMock(return_value=True)

        response = create_rule_rely(
            rule_request=rule_request, automation_service=self.automation_service
        )

        assert response == {"status": "success"}

    def test_create_rule_rely_rule_rely_error(self):
        rule_request = schemas.RuleRequestRely(
            rule_definition_id=1,
            data_volume=True,
            data_unit=False,
            sms_unit=True,
            voice_unit=False,
            threshold=True,
            data_percentage=False,
            view_deactivate_sim=True,
            required_deactivate_sim=False,
            view_rate_plan_change=False,
            required_rate_plan_change=True,
            add_any_rate_plan=True,
            is_monthly_pool=True,
            view_email=True,
            view_sms=False,
            view_push=True,
            voice_mo_percentage=True,
            voice_mt_percentage=False,
            voice_momt_percentage=False,
            sms_percentage=False,
            data=True,
            voice_mo=True,
            voice_mt=False,
            sms=False,
        )
        self.automation_service.create_rule_rely.side_effect = RuleRelyError(
            "Rule rely creation failed."
        )

        with pytest.raises(HTTPException) as exc:
            create_rule_rely(
                rule_request=rule_request, automation_service=self.automation_service
            )
        assert exc.value.status_code == 404
        assert exc.value.detail == "Rule rely creation failed."

    def test_create_rule_rely_forbidden_error(self):
        rule_request = schemas.RuleRequestRely(
            rule_definition_id=1,
            data_volume=False,
            data_unit=True,
            sms_unit=False,
            voice_unit=True,
            threshold=False,
            data_percentage=False,
            view_deactivate_sim=False,
            required_deactivate_sim=True,
            view_rate_plan_change=True,
            required_rate_plan_change=False,
            add_any_rate_plan=True,
            is_monthly_pool=True,
            view_email=False,
            view_sms=True,
            view_push=False,
            voice_mo_percentage=True,
            voice_mt_percentage=False,
            voice_momt_percentage=False,
            sms_percentage=False,
            data=True,
            voice_mo=True,
            voice_mt=False,
            sms=False,
        )
        self.automation_service.create_rule_rely.side_effect = ForbiddenError(
            "You are not authorized."
        )

        with pytest.raises(HTTPException) as exc:
            create_rule_rely(
                rule_request=rule_request, automation_service=self.automation_service
            )
        assert exc.value.status_code == 403
        assert exc.value.detail == "You are trying to access a restricted area."

    def test_create_rule_rely_value_error(self):
        rule_request = schemas.RuleRequestRely(
            rule_definition_id=1,
            data_volume=True,
            data_unit=False,
            sms_unit=True,
            voice_unit=False,
            threshold=True,
            data_percentage=False,
            view_deactivate_sim=True,
            required_deactivate_sim=False,
            view_rate_plan_change=False,
            required_rate_plan_change=True,
            add_any_rate_plan=True,
            is_monthly_pool=True,
            view_email=True,
            view_sms=False,
            view_push=True,
            voice_mo_percentage=True,
            voice_mt_percentage=False,
            voice_momt_percentage=False,
            sms_percentage=False,
            data=True,
            voice_mo=True,
            voice_mt=False,
            sms=False,
        )
        self.automation_service.create_rule_rely.side_effect = ValueError(
            "Invalid data provided."
        )

        with pytest.raises(HTTPException) as exc:
            create_rule_rely(
                rule_request=rule_request, automation_service=self.automation_service
            )
        assert exc.value.status_code == 422
        assert exc.value.detail == "We are unable to process your request."

    def test_create_rule_rely_generic_exception(self):
        rule_request = schemas.RuleRequestRely(
            rule_definition_id=1,
            data_volume=False,
            data_unit=True,
            sms_unit=False,
            voice_unit=True,
            threshold=False,
            data_percentage=False,
            view_deactivate_sim=False,
            required_deactivate_sim=True,
            view_rate_plan_change=True,
            required_rate_plan_change=False,
            add_any_rate_plan=True,
            is_monthly_pool=True,
            view_email=False,
            view_sms=True,
            view_push=False,
            voice_mo_percentage=True,
            voice_mt_percentage=False,
            voice_momt_percentage=False,
            sms_percentage=False,
            data=True,
            voice_mo=True,
            voice_mt=False,
            sms=False,
        )
        self.automation_service.create_rule_rely.side_effect = Exception(
            "Unexpected error."
        )

        with pytest.raises(HTTPException) as exc:
            create_rule_rely(
                rule_request=rule_request, automation_service=self.automation_service
            )
        assert exc.value.status_code == 400
        assert exc.value.detail == "Couldn't process the request."

    def test_update_rule_rely_success(self):
        """Test successful update for `update_rule_rely` endpoint."""
        rule_rely_id = 1
        rule_request = schemas.RuleRequestRely(
            rule_definition_id=1,
            data_volume=False,
            data_unit=True,
            sms_unit=False,
            voice_unit=True,
            threshold=False,
            data_percentage=False,
            view_deactivate_sim=False,
            required_deactivate_sim=True,
            view_rate_plan_change=True,
            required_rate_plan_change=False,
            add_any_rate_plan=True,
            is_monthly_pool=True,
            view_email=False,
            view_sms=True,
            view_push=False,
            voice_mo_percentage=True,
            voice_mt_percentage=False,
            voice_momt_percentage=False,
            sms_percentage=False,
            data=True,
            voice_mo=True,
            voice_mt=True,
            sms=True,
        )

        self.automation_service.update_rule_rely = MagicMock(return_value=True)

        response = update_rule_rely(
            rule_rely_id=rule_rely_id,
            automation_service=self.automation_service,
            rule_request=rule_request,
        )

        assert response == {"status": "success"}

    def test_update_rule_rely_rule_rely_error(self):
        rule_rely_id = 1
        rule_request = schemas.RuleRequestRely(
            rule_definition_id=1,
            data_volume=True,
            data_unit=False,
            sms_unit=True,
            voice_unit=False,
            threshold=True,
            data_percentage=False,
            view_deactivate_sim=True,
            required_deactivate_sim=False,
            view_rate_plan_change=False,
            required_rate_plan_change=True,
            add_any_rate_plan=True,
            is_monthly_pool=True,
            view_email=True,
            view_sms=False,
            view_push=True,
            voice_mo_percentage=True,
            voice_mt_percentage=False,
            voice_momt_percentage=False,
            sms_percentage=False,
            data=True,
            voice_mo=True,
            voice_mt=False,
            sms=False,
        )
        self.automation_service.update_rule_rely.side_effect = RuleRelyError(
            "Rule rely creation failed."
        )

        with pytest.raises(HTTPException) as exc:
            update_rule_rely(
                rule_request=rule_request,
                automation_service=self.automation_service,
                rule_rely_id=rule_rely_id,
            )
        assert exc.value.status_code == 404
        assert exc.value.detail == "Rule rely creation failed."

    def test_update_rule_rely_forbidden_error(self):
        rule_rely_id = 1
        rule_request = schemas.RuleRequestRely(
            rule_definition_id=1,
            data_volume=False,
            data_unit=True,
            sms_unit=False,
            voice_unit=True,
            threshold=False,
            data_percentage=False,
            view_deactivate_sim=False,
            required_deactivate_sim=True,
            view_rate_plan_change=True,
            required_rate_plan_change=False,
            add_any_rate_plan=True,
            is_monthly_pool=True,
            view_email=False,
            view_sms=True,
            view_push=False,
            voice_mo_percentage=True,
            voice_mt_percentage=False,
            voice_momt_percentage=False,
            sms_percentage=False,
            data=True,
            voice_mo=True,
            voice_mt=True,
            sms=True,
        )
        self.automation_service.update_rule_rely.side_effect = ForbiddenError(
            "You are not authorized."
        )

        with pytest.raises(HTTPException) as exc:
            update_rule_rely(
                rule_request=rule_request,
                automation_service=self.automation_service,
                rule_rely_id=rule_rely_id,
            )
        assert exc.value.status_code == 403
        assert exc.value.detail == "You are trying to access a restricted area."

    def test_update_rule_rely_value_error(self):
        rule_rely_id = 1
        rule_request = schemas.RuleRequestRely(
            rule_definition_id=1,
            data_volume=True,
            data_unit=False,
            sms_unit=True,
            voice_unit=False,
            threshold=True,
            data_percentage=False,
            view_deactivate_sim=True,
            required_deactivate_sim=False,
            view_rate_plan_change=False,
            required_rate_plan_change=True,
            add_any_rate_plan=True,
            is_monthly_pool=True,
            view_email=True,
            view_sms=False,
            view_push=True,
            voice_mo_percentage=True,
            voice_mt_percentage=False,
            voice_momt_percentage=False,
            sms_percentage=False,
            data=True,
            voice_mo=True,
            voice_mt=False,
            sms=False,
        )
        self.automation_service.update_rule_rely.side_effect = ValueError(
            "Invalid data provided."
        )

        with pytest.raises(HTTPException) as exc:
            update_rule_rely(
                rule_request=rule_request,
                automation_service=self.automation_service,
                rule_rely_id=rule_rely_id,
            )
        assert exc.value.status_code == 422
        assert exc.value.detail == "We are unable to process your request."

    def test_update_rule_rely_generic_exception(self):
        rule_rely_id = 1
        rule_request = schemas.RuleRequestRely(
            rule_definition_id=1,
            data_volume=False,
            data_unit=True,
            sms_unit=False,
            voice_unit=True,
            threshold=False,
            data_percentage=False,
            view_deactivate_sim=False,
            required_deactivate_sim=True,
            view_rate_plan_change=True,
            required_rate_plan_change=False,
            add_any_rate_plan=True,
            is_monthly_pool=True,
            view_email=False,
            view_sms=True,
            view_push=False,
            voice_mo_percentage=True,
            voice_mt_percentage=False,
            voice_momt_percentage=False,
            sms_percentage=False,
            data=True,
            voice_mo=True,
            voice_mt=True,
            sms=True,
        )
        self.automation_service.update_rule_rely.side_effect = Exception(
            "Unexpected error."
        )

        with pytest.raises(HTTPException) as exc:
            update_rule_rely(
                rule_rely_id=rule_rely_id,
                rule_request=rule_request,
                automation_service=self.automation_service,
            )
        assert exc.value.status_code == 400
        assert exc.value.detail == "Couldn't process the request."

    def test_get_rule_unit_enum(self, api_security):
        mock_auth, mock_request = api_security("/v1/glass/rule/rule-unit")
        mock_request.method = "GET"

        response = get_rule_unit_enum()

        assert response == {
            "dataUnit": EnumerationList.from_enum(DataUnit),
            "voiceUnit": EnumerationList.from_enum(VoiceUnit),
            "smsUnit": EnumerationList.from_enum(SMSUnit),
            "dataPercentage": EnumerationList.from_enum(DataPercentage),
            "voiceMoPercentage": EnumerationList.from_enum(VoiceMOPercentage),
            "voiceMtPercentage": EnumerationList.from_enum(VoiceMTPercentage),
            "voiceMomtPercentage": EnumerationList.from_enum(VoiceMOMTPercentage),
            "smsPercentage": EnumerationList.from_enum(SMSPercentageUnit),
        }

    def test_get_rule_action_enum(self, api_security):
        mock_auth, mock_request = api_security("/v1/glass/rule/rule-action")
        mock_request.method = "GET"

        response = get_rule_action_enum()

        assert response == {
            "reactivateSim": EnumerationList.from_enum(ActionValue),
            "changeRatePlan": EnumerationList.from_enum(ChangeRatePlanValue),
        }

    def test_get_service_by_definition(self):
        rule_definition_code = MBDUT
        self.automation_service.get_service_by_definition = MagicMock(
            return_value=[Service.DATA]
        )
        response = self.automation_service.get_service_by_definition(
            rule_definition_code=rule_definition_code,
        )
        assert response == [Service.DATA]

    def test_create_rule_ERROR_no_rule_definition(
        self,
        request_mock,
        api_security,
        redis_service_mock,
    ):
        mock_auth, mock_request = api_security("/v1/glass/rule")
        mock_request.method = "POST"

        create_rule_request = schemas.CreateRuleRequest(**RULE_REQUEST)
        self.automation_service.create_rule.side_effect = NoRuleDefinition(
            rule_category_id=9999
        )

        with pytest.raises(HTTPException) as exc_info:
            create_rule(
                rule_request=create_rule_request,
                request=mock_request,
                background_tasks=BackgroundTasks(),
                redis_service=redis_service_mock,
                automation_service=self.automation_service,
                authorization=mock_auth,
            )
        assert exc_info.value.status_code == 404
        assert "No rule definition found for rule_category_id" in exc_info.value.detail
