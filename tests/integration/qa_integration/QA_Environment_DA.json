{"id": "7d7d6f26-4b35-4173-ba0f-44ec659db62e", "name": "QA_Environment_DA", "values": [{"key": "TokenUrl", "value": "https://sso.test.spogconnected.com/auth/realms/test-connected-platform/protocol/openid-connect/token", "type": "default", "enabled": true}, {"key": "BaseURL", "value": "https://test.spogconnected.com", "type": "default", "enabled": true}, {"key": "IMSI", "value": "***************", "type": "default", "enabled": true}, {"key": "IMSI_1", "value": "***************", "type": "default", "enabled": true}, {"key": "month", "value": "2025-06", "type": "default", "enabled": true}, {"key": "account_id", "value": "1", "type": "default", "enabled": true}, {"key": "invoice_id", "value": 462, "type": "default", "enabled": true}, {"key": "rateplanid", "value": 302, "type": "any", "enabled": true}, {"key": "AccountId", "value": 824, "type": "any", "enabled": true}, {"key": "Name", "value": "qfmce0n5c4", "type": "default", "enabled": true}, {"key": "Client_id", "value": "organization-management", "type": "secret", "enabled": true}, {"key": "Client_secret", "value": "d3df5208-c809-4063-bfba-c2254ebefd2f", "type": "secret", "enabled": true}, {"key": "Grant_type", "value": "password", "type": "secret", "enabled": true}, {"key": "Username", "value": "<EMAIL>", "type": "secret", "enabled": true}, {"key": "Password", "value": "P@ssw0rd1", "type": "secret", "enabled": true}, {"key": "fromDate", "value": "2025-06-01", "type": "any", "enabled": true}, {"key": "toDate", "value": "2025-06-29", "type": "any", "enabled": true}, {"key": "Adjustment_id", "value": 112, "type": "default", "enabled": true}, {"key": "Billing_cycle_date", "value": "2025-05", "type": "default", "enabled": true}, {"key": "Organisation_id", "value": "", "type": "default", "enabled": true}, {"key": "Billing_cycle_date_Future", "value": "2024-09", "type": "default", "enabled": true}, {"key": "Adjustment date", "value": "2025-06-01", "type": "any", "enabled": true}, {"key": "random_number", "value": "911451529429790", "type": "any", "enabled": true}, {"key": "Random_number", "value": "", "type": "any", "enabled": true}, {"key": "contractEndDate", "value": "2025-06-30", "type": "any", "enabled": true}, {"key": "payment_term", "value": "3", "type": "any", "enabled": true}, {"key": "sim_charge", "value": "61655680746555943715", "type": "any", "enabled": true}, {"key": "group_path", "value": "Nextgen Clearing", "type": "default", "enabled": true}, {"key": "resource_name", "value": "puieggm9vq", "type": "any", "enabled": true}, {"key": "resource_id", "value": "9cfafaab-4854-4047-a500-c4931284e569", "type": "any", "enabled": true}, {"key": "role_id", "value": "089cf118-04ec-418f-a393-ede6dbabd4a2", "type": "any", "enabled": true}, {"key": "discription", "value": "hvovo86h67c5mcl7vr0d", "type": "any", "enabled": true}, {"key": "permission_id", "value": "fafe3913-0df3-4a1c-8a73-1d262f4b5722", "type": "default", "enabled": true}, {"key": "search_value", "value": "DistributorAdmin", "type": "default", "enabled": true}, {"key": "role_name", "value": "spin4a74p6", "type": "default", "enabled": true}, {"key": "resource_scopes_name", "value": "yhejfm1", "type": "default", "enabled": true}, {"key": "resource_scopes_display_name", "value": "b3cvg1m", "type": "any", "enabled": true}, {"key": "resource_scopes", "value": "gg8g1ib", "type": "any", "enabled": true}, {"key": "permission_name", "value": "zzv3gdnsb4ci", "type": "default", "enabled": true}, {"key": "scope_id", "value": "47e90d69-fcd6-4210-837f-18f13b496c28", "type": "default", "enabled": true}, {"key": "group_id", "value": "99a7fdc4-39df-4cd2-a334-f6572fe9c463", "type": "any", "enabled": true}, {"key": "owner", "value": "y1eyx", "type": "any", "enabled": true}, {"key": "account_id_reallocate", "value": "15", "type": "default", "enabled": true}, {"key": "rateplan_id_reallocate", "value": "29", "type": "default", "enabled": true}, {"key": "ICCID", "value": "8944538532046590109", "type": "default", "enabled": true}, {"key": "imsi_reallocate", "value": "**************", "type": "default", "enabled": true}, {"key": "ActiveSIM_ID", "value": "1", "type": "default", "enabled": true}, {"key": "ActiveSIM_Month", "value": "2025-06", "type": "default", "enabled": true}, {"key": "account_id_get_imsi", "value": "163", "type": "default", "enabled": true}, {"key": "Carrier_Type1", "value": "GBRME", "type": "default", "enabled": true}, {"key": "Carrier_Type2", "value": "GBRVF", "type": "default", "enabled": true}, {"key": "Carrier_Type3", "value": "GBRCN", "type": "default", "enabled": true}, {"key": "resource_scopes1", "value": "uavhzuqvqv", "type": "any", "enabled": true}, {"key": "MSISDN", "value": "***************", "type": "default", "enabled": true}, {"key": "file_key", "value": ["temp_file_key", "temp_file_key"], "type": "any", "enabled": true}, {"key": "Rule_Name", "value": "cSslhCVNBL", "type": "any", "enabled": true}, {"key": "Data_Volume", "value": 6584, "type": "any", "enabled": true}, {"key": "rule_uuid", "value": "b594ff4c-7ad8-4d9b-b3ed-ed8c9b45fd96", "type": "any", "enabled": true}, {"key": "notification_id", "value": "684815c6b055c89de2e29ce1", "type": "any", "enabled": true}, {"key": "work_id", "value": "67fcfeab549e7f47f0230652", "type": "any", "enabled": true}, {"key": "month_adt", "value": "2025-06", "type": "any", "enabled": true}, {"key": "Random_Email", "value": "<EMAIL>", "type": "any", "enabled": true}, {"key": "dataVolume", "value": "null", "type": "any", "enabled": true}, {"key": "rule_ID", "value": "c3d0f796-18cf-4d51-bef3-1e51a6d24780", "type": "any", "enabled": true}, {"key": "IMSI_notification", "value": "234588570010012", "type": "any", "enabled": true}, {"key": "usage_limit_notification", "value": 5, "type": "any", "enabled": true}, {"key": "month_notification", "value": "2024-12", "type": "any", "enabled": true}, {"key": "ICCID_notification", "value": "8944538532046590125", "type": "default", "enabled": true}, {"key": "MSISDN_notification", "value": "883200000110322", "type": "default", "enabled": true}, {"key": "ModelID", "value": 25, "type": "any", "enabled": true}, {"key": "rateplan_id", "value": 3, "type": "any", "enabled": true}, {"key": "CDR_ID", "value": "684815bb3ea39ff0ae39c7b1", "type": "any", "enabled": true}, {"key": "imsi_notification", "value": "234588570010012", "type": "any", "enabled": true}, {"key": "requestTimeout", "value": "true", "type": "any", "enabled": true}, {"key": "Token_distributor", "value": "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJtOTZBd05DbUVkWGdZVGV5VFdBTDV0SXBnQkwzOW83SVI5dUVlVkx1eEtjIn0.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.De0Gjxn0kzH7hdwSi0ZTT1mE12tRM9v0FfRRl0deqvOUr2p8f2SG2Ye5K2zZpQzcAqvecgqHQljxXoqw4445oUP-AkqmQKWs8gwSnm6F1PJKR9vLsIlzRk5cWuGZaGdNZwC_-eNOs15hgfoLxsRhJ9iPiiluIErqzMjb8dmJoJSHFaHOKyaO3Dof3bGLrpiO0qDXXOuw9FnL7YtSocZJKbBKjiYrIEle-z54jTVS8Htwczyvwb28Yw7EcZC2GvJ_xspMG4p8YR7iSOrJHRFbP2Gn54LkQlc9Zyy4KOru95VOzWBWprjFzODJ3_i85xtBJJWL2aQ4Hf5_US0iRB3X0Q", "type": "secret", "enabled": true}, {"key": "randomTime", "value": "14:19:52", "type": "any", "enabled": true}, {"key": "rateplanid_1", "value": 215, "type": "any", "enabled": true}, {"key": "Target_RatePlan", "value": 50, "type": "any", "enabled": true}, {"key": "Source_RatePlan", "value": 21, "type": "any", "enabled": true}, {"key": "rateplanid_def", "value": 216, "type": "any", "enabled": true}, {"key": "account_id_rp_change", "value": "3", "type": "default", "enabled": true}, {"key": "imsi_rp_change", "value": "***************", "type": "default", "enabled": true}, {"key": "current_hours", "value": "11", "type": "any", "enabled": true}, {"key": "current_minutes", "value": "23", "type": "any", "enabled": true}, {"key": "current_seconds", "value": "43", "type": "any", "enabled": true}, {"key": "Account_id", "value": 822, "type": "default", "enabled": true}, {"key": "rule_Definition_id", "value": "1", "type": "default", "enabled": true}, {"key": "rule_Definition_id_2", "value": "4", "type": "default", "enabled": true}, {"key": "Token", "value": "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJtOTZBd05DbUVkWGdZVGV5VFdBTDV0SXBnQkwzOW83SVI5dUVlVkx1eEtjIn0.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.mjIKBjKp5dZfgOBk1xYmc7Og1Rr1LvHwtdBcO_fYM56F6LDX8VnvnZzfbePpaQfMSQOyOyrLyuu5KC3EumWANCbl5iYSrueA6Kk84pM3n4XTBOsuKsc6TeMFI1zyn-AYWDRxQrJSkop22YTqpMK_39mqEKUEBK7bz0Bl0ftOoi8HcfEUY-7qkrQRxmBy9L_Eqad7ouq_YaQAbt13Mf1oWz-9KdSbL2XQvZay0a_nj_2apZdjcUlo6lfHs-JeRbs8o3WLwM0it9Bi2dq2Vq2j2F_AOvJzV75AbTO9BnAziwvNkbmsJUzj5ILv-2qH0VAVxDhZEEvR9QduJxDfdBmC7A", "type": "any", "enabled": true}, {"key": "IMSI_Audit", "value": "234588570010002", "type": "default", "enabled": true}, {"key": "trace_id", "value": "682c117238e28b955e803648", "type": "any", "enabled": true}, {"key": "RequestID", "value": "6846ca3c5c79cf0dfb8a6ada", "type": "any", "enabled": true}, {"key": "user_id", "value": "4f239986-ad9e-4b02-81e2-b6036f0b3dc5", "type": "default", "enabled": true}, {"key": "rule_rely_id", "value": "1", "type": "default", "enabled": true}, {"key": "rule_rely_id_2", "value": "2", "type": "default", "enabled": true}, {"key": "MSISDN_Audit", "value": "************", "type": "default", "enabled": true}, {"key": "account_id_audit", "value": "13", "type": "default", "enabled": true}, {"key": "account_log_id", "value": "684c02f03c32af287431bce6", "type": "any", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2025-06-20T12:54:37.364Z", "_postman_exported_using": "Postman/11.50.4"}