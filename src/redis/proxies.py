from uuid import UUID

from api.rate_plans.schemas import CreateRatePlanRequest, UpdateRatePlanRequest
from auth.dto import AuthenticatedUser
from rate_plans.services import AbstractRatePlanService
from redis.service import AbstractRedisService


class RedisServiceProxy(AbstractRedisService):
    def __init__(
        self,
        redis_service: AbstractRedisService,
        user: AuthenticatedUser,
    ) -> None:
        self.redis_service = redis_service
        self.user = user

    def create_redis_rule(
        self,
        account_id: list[int],
    ):
        return self.redis_service.create_redis_rule(account_id=account_id)

    def update_redis_rule(
        self,
        account_id: list[int],
        rule_uuid: UUID | None = None,
    ):
        return self.redis_service.update_redis_rule(
            account_id=account_id, rule_uuid=rule_uuid
        )

    def update_rule_status(
        self,
        rule_status: bool,
        account_id: int | None = None,
        rule_uuid: UUID | None = None,
    ) -> bool:
        return self.redis_service.update_rule_status(
            rule_status=rule_status, account_id=account_id, rule_uuid=rule_uuid
        )

    def delete_redis_rule(
        self, account_id: int | None = None, rule_uuid: UUID | None = None
    ):
        return self.redis_service.delete_redis_rule(
            account_id=account_id, rule_uuid=rule_uuid
        )

    def allocate_sims(self, account_id: int, rate_plan_id: int):
        return self.redis_service.allocate_sims(account_id, rate_plan_id)

    def create_redis_rate_plan(
        self,
        rate_plan_id: int,
        rate_plan_create: CreateRatePlanRequest,
        rate_plan_service: AbstractRatePlanService,
    ):
        return self.redis_service.create_redis_rate_plan(
            rate_plan_id, rate_plan_create, rate_plan_service
        )

    def update_redis_rate_plan(
        self,
        rate_plan_id: int,
        rate_plan_update: UpdateRatePlanRequest,
        rate_plan_service: AbstractRatePlanService,
    ):
        return self.redis_service.update_redis_rate_plan(
            rate_plan_id, rate_plan_update, rate_plan_service
        )

    def delete_redis_rate_plan(self, rate_plan_id: int):
        return self.redis_service.delete_redis_rate_plan(rate_plan_id=rate_plan_id)
