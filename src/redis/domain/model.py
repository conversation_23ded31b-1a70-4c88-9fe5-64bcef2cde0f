from dataclasses import dataclass, field
from uuid import UUID

from pydantic import BaseModel, EmailStr

from api.rate_plans.schemas import CountryGroup, RateGroup
from api.schema_types import CountryCode
from common.types import Unit


@dataclass
class Action:
    name: str
    action: str
    actionValue: str


@dataclass
class RateplanAction:
    name: str
    action: str
    target: int
    actionValue: str
    source: int | None = None
    sourcePlan: str | None = None
    targetPlan: str | None = None


@dataclass
class Notification:
    name: str
    notification: bool
    email: list[EmailStr]


@dataclass
class Rules:
    status: bool
    definitionCode: str
    rulesUuidParam: UUID | None = None
    simUsageLimit: int | None = None
    unit: Unit | None = None
    sizeInBytes: int | None = None
    actions: list[Action | RateplanAction] = field(default_factory=list)
    notifications: list[Notification] = field(default_factory=list)


@dataclass
class RuleInfo:
    IMSIs: list[str]
    accountId: int
    accountName: str
    rules: list[Rules] = field(default_factory=list)


class OriginationGroup(BaseModel):
    origination_zones: set[CountryGroup | CountryCode]
    data: RateGroup
    voice_mo: RateGroup
    voice_mt: RateGroup
    sms: RateGroup


class RatePlanRequest(BaseModel):
    accountId: int
    name: str
    accessFee: float
    currency: str
    isDefault: bool = False
    simLimit: int | None = None
    allowanceUsed: float = 0.00
    originationGroups: list[OriginationGroup]
    ratePlanId: int


class RatePlanInfo(RatePlanRequest):
    ...
