from abc import abstractmethod

from fastapi.encoders import jsonable_encoder
from platform_api_client import PlatformAPIClient, PlatformAPIError
from starlette import status

from app.config import logger, settings
from auth.exceptions import ForbiddenError, NotFound, Unauthorized
from redis.domain.model import RatePlanInfo, RatePlanRequest, RuleInfo, Rules
from redis.exception import RedisAPIError


class AbstractRedisAPI:
    @abstractmethod
    def create_redis_rule(self, rule: RuleInfo):
        ...

    @abstractmethod
    def update_redis_rule(self, rule: Rules, rule_uuid: str):
        ...

    @abstractmethod
    def update_rule_status(self, rule_uuid: str):
        ...

    @abstractmethod
    def delete_redis_rule(self, rule_uuid: str):
        ...

    @abstractmethod
    def allocate_sims(self, account_id: int, rate_plan_id: int, imsis: list[str]):
        ...

    @abstractmethod
    def create_redis_rate_plan(self, rate_plan_create: RatePlanInfo):
        ...

    @abstractmethod
    def update_redis_rate_plan(
        self, rate_plan_update: RatePlanRequest, rate_plan_id: int
    ):
        ...

    @abstractmethod
    def delete_redis_rate_plan(self, rate_plan_id: int):
        ...


class HTTPRedisAPI(AbstractRedisAPI):
    CREATE_REDIS_RULE = "/v1/analytics/rule"
    UPDATE_REDIS_RULE = "/v1/analytics/rule/{rule_uuid}"
    DELETE_REDIS_RULE = "/v1/analytics/rule/{rule_uuid}"
    ALLOCATE_SIMS = "/v1/analytics/imsis/account/{account_id}/rateplan/{rate_plan_id}"
    CREATE_REDIS_RATE_PLAN = "/v1/analytics/rateplan"
    UPDATE_REDIS_RATE_PLAN = "/v1/analytics/rateplan/{rate_plan_id}"
    DELETE_REDIS_RATE_PLAN = "/v1/analytics/rateplan/{rate_plan_id}"

    def __init__(self, api_client: PlatformAPIClient):
        self.api = api_client
        self.headers = {"Content-Type": "application/json"}

    def create_redis_rule(self, rule: RuleInfo):
        try:
            url = f"{settings.APP_BASE_URL}{self.CREATE_REDIS_RULE}"
            logger.debug(f"Creating redis rule url: {url}")
            response = self.api.post(
                url, headers=self.headers, json=jsonable_encoder(rule)
            )
            if response.status_code == 201:
                return response.json()
            else:
                raise RedisAPIError
        except PlatformAPIError as e:
            logger.error(f"Error in create_redis_rule. Error: {str(e)}")
            match e.status_code:
                case status.HTTP_401_UNAUTHORIZED:
                    raise Unauthorized("Unauthorized access.")
                case status.HTTP_403_FORBIDDEN:
                    raise ForbiddenError()
                case _:
                    logger.error(f"Error in create_redis_rule. Error: {str(e)}")
        except ConnectionError:
            raise ConnectionError("Failed to connect with the Redis API")

    def update_redis_rule(self, rule: Rules, rule_uuid: str):
        try:
            url = f"{settings.APP_BASE_URL}{self.UPDATE_REDIS_RULE.format(rule_uuid=rule_uuid)}"  # noqa
            logger.debug(f"Updating redis rule url: {url}")
            response = self.api.put(
                url, headers=self.headers, json=jsonable_encoder(rule)
            )
            if response.status_code == 202:
                return response.json()
            else:
                raise RedisAPIError
        except PlatformAPIError as e:
            logger.error(f"Error in update_redis_rule. Error: {str(e)}")
            match e.status_code:
                case status.HTTP_401_UNAUTHORIZED:
                    raise Unauthorized("Unauthorized access.")
                case status.HTTP_403_FORBIDDEN:
                    raise ForbiddenError()
                case _:
                    logger.error(f"Error in update_redis_rule. Error: {str(e)}")
        except ConnectionError:
            raise ConnectionError("Failed to connect with the Redis API")

    def update_rule_status(self, rule_uuid: str):
        try:
            url = f"{settings.APP_BASE_URL}{self.DELETE_REDIS_RULE.format(rule_uuid=rule_uuid)}"  # noqa
            logger.debug(f"Getting rule status url: {url}")
            response = self.api.delete(url, headers=self.headers)
            if response.status_code == 202:
                return response.json()
            else:
                raise RedisAPIError
        except PlatformAPIError as e:
            logger.error(f"Error in get_rule_status. Error: {str(e)}")
            match e.status_code:
                case status.HTTP_401_UNAUTHORIZED:
                    raise Unauthorized("Unauthorized access.")
                case status.HTTP_403_FORBIDDEN:
                    raise ForbiddenError()
                case _:
                    logger.error(f"Error in get_rule_status. Error: {str(e)}")
        except ConnectionError:
            raise ConnectionError("Failed to connect with the Redis API")

    def delete_redis_rule(self, rule_uuid: str):
        try:
            url = f"{settings.APP_BASE_URL}{self.DELETE_REDIS_RULE.format(rule_uuid=rule_uuid)}"  # noqa
            logger.debug(f"Deleting redis rule url: {url}")
            response = self.api.delete(url, headers=self.headers)
            if response.status_code == 202:
                return response.json()
            else:
                raise RedisAPIError
        except PlatformAPIError as e:
            logger.error(f"Error in delete_redis_rule. Error: {str(e)}")
            match e.status_code:
                case status.HTTP_401_UNAUTHORIZED:
                    raise Unauthorized("Unauthorized access.")
                case status.HTTP_403_FORBIDDEN:
                    raise ForbiddenError()
                case _:
                    logger.error(f"Error in delete_redis_rule. Error: {str(e)}")
        except ConnectionError:
            raise ConnectionError("Failed to connect with the Redis API")

    def allocate_sims(self, account_id: int, rate_plan_id: int, imsis: list[str]):
        try:
            url = f"{settings.APP_BASE_URL}{self.ALLOCATE_SIMS.format(account_id=account_id, rate_plan_id=rate_plan_id)}"  # noqa
            logger.debug(f"Allocating sims url: {url}")
            response = self.api.put(
                url, headers=self.headers, json=jsonable_encoder({"IMSIs": imsis})
            )
            logger.debug(f"Response -> {response.status_code} and {response.json()}")
            if response.status_code == 202:
                return response.json()
            else:
                raise RedisAPIError
        except PlatformAPIError as e:
            logger.error(f"Error in allocate_sims. Error: {str(e)}")
            match e.status_code:
                case status.HTTP_401_UNAUTHORIZED:
                    raise Unauthorized("Unauthorized access.")
                case status.HTTP_403_FORBIDDEN:
                    raise ForbiddenError()
                case _:
                    logger.error(f"Error in allocate_sims. Error: {str(e)}")
        except ConnectionError:
            raise ConnectionError("Failed to connect with the Redis API")

    def create_redis_rate_plan(self, rate_plan_create: RatePlanInfo):
        try:
            url = f"{settings.APP_BASE_URL}{self.CREATE_REDIS_RATE_PLAN}"
            logger.debug(f"Creating redis rate plan url: {url}")
            response = self.api.post(
                url, headers=self.headers, json=jsonable_encoder(rate_plan_create)
            )
            if response.status_code == 201:
                return response.json()
            else:
                raise RedisAPIError
        except PlatformAPIError as e:
            logger.error(f"Error in create_redis_rate_plan. Error: {str(e)}")
            match e.status_code:
                case status.HTTP_401_UNAUTHORIZED:
                    raise Unauthorized("Unauthorized access.")
                case status.HTTP_403_FORBIDDEN:
                    raise ForbiddenError()
                case _:
                    logger.error(f"Error in create_redis_rate_plan. Error: {str(e)}")
        except ConnectionError:
            raise ConnectionError("Failed to connect with the Redis API")

    def update_redis_rate_plan(
        self, rate_plan_update: RatePlanRequest, rate_plan_id: int
    ):
        try:
            url = f"{settings.APP_BASE_URL}{self.UPDATE_REDIS_RATE_PLAN.format(rate_plan_id=rate_plan_id)}"  # noqa
            logger.debug(f"Updating redis rate plan url: {url}")
            response = self.api.put(
                url, headers=self.headers, json=jsonable_encoder(rate_plan_update)
            )
            if response.status_code == 202:
                return response.json()
            else:
                raise RedisAPIError
        except PlatformAPIError as e:
            logger.error(f"Error in update_redis_rate_plan. Error: {str(e)}")
            match e.status_code:
                case status.HTTP_401_UNAUTHORIZED:
                    raise Unauthorized("Unauthorized access.")
                case status.HTTP_403_FORBIDDEN:
                    raise ForbiddenError()
                case status.HTTP_404_NOT_FOUND:
                    raise NotFound
                case _:
                    logger.error(f"Error in update_redis_rate_plan. Error: {str(e)}")
        except ConnectionError:
            raise ConnectionError("Failed to connect with the Redis API")

    def delete_redis_rate_plan(self, rate_plan_id: int):
        try:
            url = f"{settings.APP_BASE_URL}{self.DELETE_REDIS_RATE_PLAN.format(rate_plan_id=rate_plan_id)}"  # noqa
            logger.debug(f"Deleting redis rate plan url: {url}")
            response = self.api.delete(url, headers=self.headers)
            if response.status_code == 202:
                return response.json()
            else:
                raise RedisAPIError
        except PlatformAPIError as e:
            logger.error(f"Error in delete_redis_rate_plan. Error: {str(e)}")
            match e.status_code:
                case status.HTTP_401_UNAUTHORIZED:
                    raise Unauthorized("Unauthorized access.")
                case status.HTTP_403_FORBIDDEN:
                    raise ForbiddenError()
                case _:
                    logger.error(f"Error in delete_redis_rate_plan. Error: {str(e)}")
        except ConnectionError:
            raise ConnectionError("Failed to connect with the Redis API")


class FakeRedisAPI(AbstractRedisAPI):
    def __init__(self):
        self.get_imsis_usages = {}

    def create_redis_rule(self, rule: RuleInfo):
        ...

    def update_redis_rule(self, rule: Rules, rule_uuid: str):
        ...

    def update_rule_status(self, rule_uuid: str):
        ...

    def delete_redis_rule(self, rule_uuid: str):
        ...

    def allocate_sims(self, account_id: int, rate_plan_id: int, imsis: list[str]):
        ...
