import logging
from functools import cache
from typing import Callable, Generator
from uuid import UUID

from fastapi import Depends, Header, HTTPException, status
from sqlalchemy.orm import Session

import auth.dto
from accounts.adapters.repository import DatabaseAccountRepository
from accounts.domain.ports import AbstractAccountRepository
from app.config import settings
from app.db.session import make_session
from app.platform import (
    fake_redis_api_client,
    get_mail_api_client,
    get_redis_api_client,
)
from app.s3 import get_s3_client
from app.security import make_auth_service_factory
from auth.dto import AuthenticatedActor, AuthenticatedUser
from auth.permissions import is_distributor_staff
from auth.services import (
    AbstractAuthService,
    AccountAuthService,
    FakeAuthService,
    TokenIntrospectionAuthService,
)
from common.file_storage import AbstractFileStorage, LocalFileStorage, S3FileStorage
from common.utils import trace_id_var
from mail_delivery.service import (
    AbstractMailService,
    FakeMailServiceAPI,
    MailServiceAPI,
)
from redis.adapters.externalapi import AbstractRedisAP<PERSON>, HTTPRedisAPI
from redis.adapters.repository import AbstractRepository, DatabaseRepository
from redis.proxies import RedisServiceProxy
from redis.service import AbstractRedisService, RedisService
from streaming.streaming import HTTPKafkaAPI


def get_trace_id(x_trace_id: UUID = Header(None)):
    return trace_id_var.get()


def get_db_session() -> Generator[Session, None, None]:
    session = make_session()
    try:
        yield session
    except Exception as e:
        logging.info(f"Iteration exceeded. Error {e}")
        if hasattr(e, "detail") or hasattr(e, "status_code"):
            if hasattr(e, "detail"):
                logging.info(f"{e.detail}")  # type: ignore
            if hasattr(e, "status_code"):
                logging.info(f"{e.status_code}")  # type: ignore
        else:
            logging.info(f"Iteration exceeded. Error {e}")
    finally:
        session.close()


@cache
def _fake_auth_service() -> AbstractAuthService:
    return FakeAuthService()


_get_auth_service: Callable[[], AbstractAuthService]
if not settings.OIDC_CLIENT_ID:
    _get_auth_service = _fake_auth_service
else:
    _get_auth_service = make_auth_service_factory()


def _get_account_repository(
    session: Session = Depends(get_db_session),
) -> AbstractAccountRepository:
    return DatabaseAccountRepository(session)


def get_auth_service(
    auth_service: AbstractAuthService = Depends(_get_auth_service),
    account_repository: AbstractAccountRepository = Depends(_get_account_repository),
) -> AbstractAuthService:
    def get_auth_account(organization_id: int) -> auth.dto.Account | None:
        account = account_repository.get_by_organization_id(organization_id)
        if account is None:
            return None
        return auth.dto.Account(id=account.id)

    return AccountAuthService(auth_service, get_auth_account)


def get_authenticated_user(
    auth_service: AbstractAuthService = Depends(get_auth_service),
) -> AuthenticatedUser:
    return auth_service.authenticated_user


def get_authenticated_actor(
    auth_service: AbstractAuthService = Depends(get_auth_service),
) -> AuthenticatedActor:
    return auth_service.authenticated_actor


def distributor_access_level(exc_status_code: int = status.HTTP_403_FORBIDDEN):
    def factory(
        authenticated_user: AuthenticatedUser = Depends(get_authenticated_user),
    ):
        if not is_distributor_staff(authenticated_user):
            raise HTTPException(exc_status_code)

    return factory


def get_file_storage_object(bucket: str | None) -> Callable[[], AbstractFileStorage]:
    def generate_storage_object() -> AbstractFileStorage:
        storage: AbstractFileStorage
        if settings.APPLICATION_ENV == "local":
            return LocalFileStorage("static")
        elif settings.APPLICATION_ENV == "prod" or settings.S3_ACCESS_KEY_ID:
            if not bucket:
                logging.error("No S3 Bucket provided")
                raise RuntimeError("No S3 Bucket provided")
            return S3FileStorage(client=get_s3_client(), bucket_name=bucket)
        else:
            return LocalFileStorage("static")

    return generate_storage_object


def get_repository(
    session: Session = Depends(get_db_session),
) -> AbstractRepository:
    return DatabaseRepository(session)


def get_redis_client(
    authorization_service: AbstractAuthService = Depends(get_auth_service),
) -> Generator[AbstractRedisAPI, None, None]:
    auth_token: str | None
    match authorization_service:
        case TokenIntrospectionAuthService(token=_token):
            auth_token = _token
        case AccountAuthService(
            auth_service=TokenIntrospectionAuthService(token=_token)
        ):
            auth_token = _token
        case AccountAuthService(auth_service=FakeAuthService()) | FakeAuthService():
            auth_token = None
        case _:
            raise AssertionError(
                f"Unexpected auth service: {type(authorization_service)}"
            )

    if auth_token:
        with get_redis_api_client(auth_token) as client:
            yield HTTPRedisAPI(client)
    else:
        yield fake_redis_api_client()


def redis_service(
    repository: AbstractRepository = Depends(get_repository),
    authenticated_user: AuthenticatedUser = Depends(get_authenticated_user),
    redis_api: AbstractRedisAPI = Depends(get_redis_client),
) -> AbstractRedisService:
    return RedisServiceProxy(
        redis_service=RedisService(
            repository,
            redis_api,
        ),
        user=authenticated_user,
    )


def get_streaming_service():
    return HTTPKafkaAPI()


@cache
def fake_mail_service_api() -> AbstractMailService:
    return FakeMailServiceAPI()


def get_mail_service_client(
    authorization_service: AbstractAuthService = Depends(get_auth_service),
) -> Generator[AbstractMailService, None, None]:
    auth_token: str | None
    match authorization_service:
        case TokenIntrospectionAuthService(token=_token):
            auth_token = _token
        case AccountAuthService(
            auth_service=TokenIntrospectionAuthService(token=_token)
        ):
            auth_token = _token
        case AccountAuthService(auth_service=FakeAuthService()) | FakeAuthService():
            auth_token = None
        case _:
            raise AssertionError(
                f"Unexpected auth service: {type(authorization_service)}"
            )
    if auth_token:
        with get_mail_api_client(auth_token) as client:
            yield MailServiceAPI(client)
    else:
        yield fake_mail_service_api()
