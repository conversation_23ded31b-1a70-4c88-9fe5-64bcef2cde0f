import logging
from functools import partial
from typing import Any, Iterator
from uuid import UUID

import anyio
from fastapi import (
    APIRouter,
    BackgroundTasks,
    Depends,
    File,
    HTTPException,
    Path,
    Query,
    Request,
    Response,
    UploadFile,
    status,
)
from psycopg2 import ProgrammingError  # type: ignore
from requests.exceptions import HTTPError, RequestException, Timeout

import api.deps as trace_id_deps
from accounts.domain.exceptions import SimAccountDataNotFound
from accounts.services import AbstractAccountService
from api.accounts.deps import get_account_service_proxy
from api.authorization.deps import authorization_service
from api.decorators import (
    check_permissions,
    convert_response_to_csv,
    measure_execution_time,
)
from api.deps import get_streaming_service, redis_service
from api.rating import deps as rdeps
from api.renders import make_csv_renderer
from api.responses import CSVFileResponse
from api.schema_types import IMSI, PaginatedResponse
from api.sim import deps
from api.sim.schemas import (
    SIMAPN,
    ActiveSimMonthlyStatistic,
    Allocation,
    AllocationSummary,
    BulkUpdateRequest,
    CellLocationResponse,
    ConnectionSummary,
    CreateAllocation,
    CreateRangeRequest,
    CreateRangeResponse,
    DataSessionResponse,
    IMSIDeleteResponse,
    IMSIRangeCSVRequest,
    LocationItemResponse,
    MonthUsage,
    MsisdnCountDetails,
    MsisdnPool,
    MsisdnPoolExport,
    PushBulkIMSI,
    PushIMSI,
    PushSMS,
    Range,
    ReAllocation,
    ResponseModel,
    SIMActivateResponse,
    SIMAPNResponse,
    SIMCard,
    SIMCardAuditLogs,
    SIMCardExport,
    SIMCardsRemains,
    SimCDRHistory,
    SimCDRHistoryExport,
    SIMDeactivatedResponse,
    SIMFetchMNCMCCResponse,
    SIMFlushResponse,
    SIMPODResponse,
    SIMSendSMSResponse,
    SimSMSCDRHistory,
    SimSMSCDRHistoryExport,
    SimStatusDetails,
    SIMStatusResponse,
    SimUsage,
    SimUsageExport,
    SimVoiceCDRHistory,
    SimVoiceCDRHistoryExport,
    UnallocateSimCardDetails,
    UpdateSimCardDetailsResult,
    UploadCustomIMSI,
    UploadSimCardsRequest,
)
from app.config import log_activity, logger
from app.socket_server import sim_events
from auth.exceptions import AuthException, ForbiddenError, Unauthorized
from authorization.domain.ports import AbstractAuthorizationAPI
from cdrdata.exceptions import NoTableExist
from common.constants import ALLOCATED, IMSI_FIELD, MSISDN_FIELD, REQUEST_MESSAGE
from common.ip_utils import get_client_ip
from common.ordering import Ordering
from common.pagination import InvalidPage, Pagination
from common.parser import ParsingError
from common.searching import Searching
from common.types import MSISDN, FormFactor, Month, UploadFileStatus
from rating.storage_sync import CDRStorage
from redis.exception import RedisAPIError
from redis.service import RedisService
from sim.domain import model
from sim.exceptions import (
    AllocationDeletionNotAllowed,
    AllocationDoesNotExist,
    AllocationError,
    AlreadyExist,
    BulkProcessingError,
    EIDExitError,
    EIDsAreNotUnique,
    FileSizeExceededLimit,
    IMSIDoesNotExit,
    IMSIError,
    IMSINotAllocated,
    IMSINotAvailableForAllocation,
    IMSINotBelongToSameAccount,
    ImsisAreNotContinuity,
    ImsisAreNotUnique,
    IMSIWiseError,
    InsufficientIMSI,
    MediaError,
    MSISDNExitError,
    MSISDNNotFound,
    NoActiveSimFound,
    NoAuditLogs,
    NoConnectionHistory,
    NoSimCardsStatus,
    NoSimFound,
    NotFound,
    NotValidFileName,
    PplAuthError,
    PplUnknownSubscriber,
    RangeDoesNotExist,
    RangeIntegrityError,
    RangeNotReady,
    RatePlanAccountMappingError,
    RatePlanChangeNotAllowed,
    RatePlanException,
    RatePlanNotFound,
    ReAllocationCountError,
    ReAllocationError,
    ReAllocationToSameAccount,
    SimAccountProfileError,
    SimActivationError,
    SIMActiveCallError,
    SimActivityError,
    SimAPNError,
    SimCardImsiAlreadyExist,
    SimCardsNotFound,
    SimDeActivationError,
    SimError,
    SimFetchMNCMCCError,
    SIMFlushError,
    SimLimitCountError,
    SIMPODError,
    SIMSendSMSError,
    UnallocationException,
    WorkItemIdNotFound,
)
from sim.factory import SIMCardCSVFormat, generate_sim_cards_csv
from sim.parser import (
    EIDCSVParser,
    SIMCardCSVParser,
    parse_excel_file,
    parse_imsi_msisdn_csv,
    parse_msisdn_file,
    validate_file_extension,
    validate_sim_cards,
    validate_sim_cards_and_eid,
)
from sim.services import AbstractSimService
from streaming.streaming import AbstractKafkaAPI

router = APIRouter(tags=["sim"], prefix="/sim")
stream_logs = logging.getLogger(__name__)


@router.get("/ranges", response_model=PaginatedResponse[Range])
@check_permissions
def get_ranges(
    request: Request,
    sim_service: AbstractSimService = Depends(deps.sim_service),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
    pagination: Pagination = Depends(Pagination.query()),
    ordering: Ordering = Depends(
        Ordering.query(
            model.Range,  # type: ignore
            default_ordering="created_at",
            ordering_fields=(
                "id",
                "title",
                "quantity",
                "form_factor",
                "imsi_first",
                "imsi_last",
                "remaining",
                "created_at",
                "created_by",
            ),
        )
    ),
    searching: Searching
    | None = Depends(
        Searching.build(
            fields={
                "title",
                "quantity",
                "form_factor",
                "imsi_first",
                "imsi_last",
                "remaining",
                "created_at",
                "created_by",
            },
        )
    ),
) -> PaginatedResponse[Range]:
    records, records_count = sim_service.get_ranges(
        pagination=pagination, searching=searching, ordering=ordering
    )
    return PaginatedResponse.from_iterable(
        pagination=pagination,
        results=list(map(Range.from_model, records)),
        total_count=records_count,
    )


@router.post(
    "/ranges", response_model=CreateRangeResponse, status_code=status.HTTP_201_CREATED
)
def create_empty_range(
    range_in: CreateRangeRequest,
    sim_service: AbstractSimService = Depends(deps.sim_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> model.Range:
    """Create new range."""
    return sim_service.create_empty_range(**range_in.dict(), created_by="John Billing")


@router.post(
    "/documents", response_model=ResponseModel, status_code=status.HTTP_201_CREATED
)
@check_permissions
def create_range(
    request: Request,
    background_tasks: BackgroundTasks,
    form_data: UploadSimCardsRequest = Depends(),
    sim_service: AbstractSimService = Depends(deps.sim_service),
    account_service: AbstractAccountService = Depends(get_account_service_proxy),
    streaming_service: AbstractKafkaAPI = Depends(get_streaming_service),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> ResponseModel:
    """Create new range."""
    try:
        source_endpoint = request.scope["endpoint"].__name__.replace("_", " ").title()
        client_ip = get_client_ip(request)
        upload_status = partial(
            sim_service.add_upload_file_status,
            trace_id=trace_id,
            field=IMSI_FIELD,
            source_endpoint=source_endpoint,
            client_ip=client_ip,
            account_service=account_service,
        )
        upload_status()
        file_name = form_data.file.filename
        validate_file_extension(file_name)
        if form_data.form_factor == FormFactor.eSIM_MFF2_eUICC:
            sim_eid = EIDCSVParser(file=form_data.file.file)
            valid_sim_cards = validate_sim_cards_and_eid(sim_eid)
        else:
            sim_cards = SIMCardCSVParser(file=form_data.file.file)
            valid_sim_cards = validate_sim_cards(sim_cards)

        sim_service._validate_imsi_range(valid_sim_cards)

        background_tasks.add_task(
            sim_service.create_range,
            title=form_data.title,
            form_factor=form_data.form_factor,
            sim_cards=valid_sim_cards,
            client_ip=client_ip,
            trace_id=trace_id,
            source_endpoint=source_endpoint,
            account_service=account_service,
            streaming_service=streaming_service,
        )

        return ResponseModel(message=REQUEST_MESSAGE, request_id=str(trace_id))
    except (
        ImsisAreNotUnique,
        ImsisAreNotContinuity,
        SimCardImsiAlreadyExist,
        EIDsAreNotUnique,
    ) as e:
        upload_status(message=str(e))
        logger.error((f"{str(e)}"))
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )
    except (SimError, ParsingError, EIDExitError) as e:
        upload_status(message=str(e))
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except MSISDNExitError as e:
        upload_status(message=str(e))
        logger.error(f"Error during creating range: {str(e)}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error(f"Error during creating range: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="We Couldn't process the request.",
        )


@router.delete(
    "/ranges/{id}",
    status_code=status.HTTP_204_NO_CONTENT,
    response_class=Response,
    responses={
        404: {"description": "Range does not exist."},
    },
    deprecated=True,
)
@check_permissions
def delete_range(
    request: Request,
    range_id: int = Path(alias="id", gt=0),
    sim_service: AbstractSimService = Depends(deps.sim_service),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> None:
    """Deletes range by range_id."""
    try:
        sim_service.remove_range(range_id)
    except RangeDoesNotExist as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except RangeIntegrityError as e:
        raise HTTPException(status.HTTP_409_CONFLICT, detail=str(e))


@router.get("/allocations", response_model=PaginatedResponse[AllocationSummary])
@check_permissions
def get_allocations(
    request: Request,
    sim_service: AbstractSimService = Depends(deps.sim_service),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
    pagination: Pagination = Depends(Pagination.query()),
) -> PaginatedResponse[AllocationSummary]:
    try:
        response, total_count = sim_service.get_allocations(pagination=pagination)
        return PaginatedResponse.from_iterable(
            pagination=pagination,
            results=list(map(AllocationSummary.from_model, response)),
            total_count=total_count,
        )
    except NotFound as e:
        logger.error(f"Allocation details not found error.: {str(e)}")
        raise HTTPException(
            status.HTTP_404_NOT_FOUND, detail="Allocation details not found."
        )
    except MediaError as e:
        logger.error("Media error occured while updating account details.")
        raise HTTPException(status.HTTP_400_BAD_REQUEST, str(e))


@router.post(
    "/allocations",
    response_model=Allocation,
    status_code=status.HTTP_201_CREATED,
    deprecated=True,
)
@check_permissions
def create_allocation(
    request: Request,
    allocation_input: CreateAllocation,
    sim_service: AbstractSimService = Depends(deps.sim_service),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> model.Allocation:
    try:
        allocation = sim_service.add_allocation(allocation_input.to_model())
    except (
        RangeDoesNotExist,
        InsufficientIMSI,
        SimCardsNotFound,
        RatePlanException,
    ) as e:
        logger.error(f"An error occoured while creating allocation.:{str(e)}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except RangeNotReady as e:
        logger.error(f"A conflict occoured while creating allocation.:{str(e)}")
        raise HTTPException(status_code=status.HTTP_409_CONFLICT, detail=str(e))
    except (Exception, AssertionError) as e:
        logger.error(f"Unexpected error: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST,
            "We couldn't process your request.",
        )
    return allocation


@router.delete(
    "/allocations",
    status_code=status.HTTP_204_NO_CONTENT,
    response_class=Response,
    responses={
        404: {"description": "Range does not exist."},
    },
    deprecated=True,
)
@check_permissions
def remove_allocations(
    request: Request,
    range_id: int = Query(gt=0),
    sim_service: AbstractSimService = Depends(deps.sim_service),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> None:
    """Deletes allocations by range_id."""
    try:
        return sim_service.remove_allocations_by_range_id(range_id)
    except RangeDoesNotExist as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))


@router.delete(
    "/allocations/{id}",
    status_code=status.HTTP_204_NO_CONTENT,
    response_class=Response,
    responses={
        404: {"description": "Allocation does not exist."},
        400: {"descriptions": "Allocation must be the last in the range."},
    },
    deprecated=True,
)
@check_permissions
def remove_allocation(
    request: Request,
    allocation_id: int = Path(alias="id", gt=0),
    sim_service: AbstractSimService = Depends(deps.sim_service),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
):
    """Deletes only last allocation."""
    try:
        return sim_service.remove_allocation(allocation_id)
    except AllocationDoesNotExist:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND)
    except AllocationDeletionNotAllowed as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


def get_sims(
    sim_service: AbstractSimService = Depends(deps.sim_service),
    pagination: Pagination = Depends(Pagination.query()),
    ordering: Ordering = Depends(
        Ordering.query(
            SIMCard,
            default_ordering="iccid",
            ordering_fields=(
                "iccid",
                "id",
                "msisdn",
                "imsi",
                "form_factor",
                "allocation_reference",
                "allocation_date",
                "sim_status",
            ),
        )
    ),
    account_id: int | None = None,
    searching: Searching
    | None = Depends(
        Searching.build(
            fields={
                "iccid",
                "msisdn",
                "imsi",
                "form_factor",
            },
        )
    ),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> PaginatedResponse[SIMCard]:
    try:
        sim_cards = sim_service.get_sim_cards(
            account_id=account_id,
            pagination=pagination,
            ordering=ordering,
            searching=searching,
        )
        total_count = sim_service.get_sim_count(
            account_id=account_id, searching=searching
        )
        return PaginatedResponse.from_iterable(
            pagination=pagination,
            results=list(map(SIMCard.from_model, sim_cards)),
            total_count=total_count,
        )

    except (Exception, AssertionError) as e:
        logger.error(f"Unexpected error: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST,
            "We couldn't process your request.",
        )


router.get("/cards", response_model=PaginatedResponse[SIMCard])(get_sims)


@check_permissions
@measure_execution_time
def get_sims_export(
    request: Request,
    account_id: int | None = None,
    sim_service: AbstractSimService = Depends(deps.sim_service),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> Iterator[SIMCard]:
    try:
        records = sim_service.get_sim_cards_export(
            account_id=account_id,
        )

        return map(SIMCard.from_model, records)
    except NoSimFound as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except RatePlanNotFound as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)
        )
    except AssertionError as e:
        logger.error(f"Assertion error: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST,
            "We couldn't process your request.",
        )
    except (ValueError, Exception) as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


router.get(
    "/cards/export",
    summary="Export SIM cards to csv",
    response_class=CSVFileResponse,
)(convert_response_to_csv(SIMCardExport, "SimCards")(get_sims_export))


@router.get("/cards/remains", response_model=list[SIMCardsRemains])
@check_permissions
def get_sim_remains(
    request: Request,
    sim_service: AbstractSimService = Depends(deps.sim_service),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> list[SIMCardsRemains]:
    sim_remains: dict[str, dict[str, Any]] = {}
    for provider, form_factor, remaining in sim_service.get_sim_remains():
        sim_remains.setdefault(provider, {"provider": provider})[
            form_factor.name.lower()
        ] = remaining
    return list(map(SIMCardsRemains.parse_obj, sim_remains.values()))


@router.post("/documents/test", response_class=CSVFileResponse)
def generate_test_sim_cards(imsi_range: IMSIRangeCSVRequest):
    records = generate_sim_cards_csv(imsi_range.imsi_first, imsi_range.length)
    render = make_csv_renderer(SIMCardCSVFormat, delimiter=",")
    return CSVFileResponse(
        content=render(list(records)),
        filename=f"SimRange_{imsi_range.imsi_first}_{imsi_range.length}.csv",
    )


@router.get(
    "/cards/status/{imsi}",
    status_code=status.HTTP_200_OK,
    response_model=SIMStatusResponse,
)
@log_activity(
    stream_logs, ignored_params=("sim_service",), sensitive_values=("imsi", "msisdn")
)
@check_permissions
def get_sim_status(
    request: Request,
    imsi: IMSI,
    sim_service: AbstractSimService = Depends(deps.sim_service),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> model.SIMStatusResponse:
    """API for get SIM status"""
    try:
        client_ip = get_client_ip(request)
        return sim_service.sim_status(imsi=imsi, created_by="", client_ip=client_ip)
    except Unauthorized as e:
        logger.error(f"Unauthorized.: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, detail="Unauthorized access."
        )
    except IMSINotAllocated as e:
        logger.error((f"{str(e)}"))
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"Requested imsi {imsi} not allocated to the requested account.",
        )
    except (SimCardsNotFound, AllocationError) as e:
        logger.error(f"Not found error occoured.:{str(e)}")
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except PplAuthError as e:
        logger.error(f"PPL auth error.:{str(e)}")
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail=str(e))
    except PplUnknownSubscriber as e:
        logger.error(f"We couldn't process your request.:{str(e)}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except (Exception, AssertionError) as e:
        logger.error(f"We couldn't process your request.: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="We couldn't process your request.",
        )


@router.post(
    "/cards/activate",
    status_code=status.HTTP_201_CREATED,
    response_model=SIMActivateResponse,
)
@log_activity(stream_logs, ignored_params=("sim_service",))
@check_permissions
def sim_activate(
    request: Request,
    imsi: PushIMSI,
    sim_service: AbstractSimService = Depends(deps.sim_service),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> model.SIMActivateResponse:
    """API for get SIM activate"""
    try:
        client_ip = get_client_ip(request)
        response = sim_service.activate_sim(
            imsi.imsi,
            imsi.created_by,
            client_ip,
        )
        anyio.from_thread.run(
            lambda: sim_events.sim_details_update([imsi.imsi], response.status)
        )
        return response
    except SimAccountProfileError as e:
        logger.error(f"Sim profile Error:-.: {str(e)}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=f"{str(e)}")
    except Unauthorized as e:
        logger.error(f"Unauthorized.: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, detail="Unauthorized access."
        )
    except SimCardsNotFound as e:
        logger.error(f"Sim card not found.:{str(e)}")
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except SimActivityError as e:
        logger.error(f"Sim activity error occoured.:{str(e)}")
        raise HTTPException(status_code=status.HTTP_409_CONFLICT, detail=str(e))
    except PplAuthError as e:
        logger.error(f"PPL auth error.:{str(e)}")
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail=str(e))
    except SimActivationError as e:
        logger.error(f"Sim activation error.:{str(e)}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except IMSINotAllocated as e:
        logger.error(f"IMSI not allocated.:{str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid IMSI.",
        )
    except SimError as e:
        logger.error(f"Sim bulk process error: {str(e)}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except (Exception, AssertionError) as e:
        logger.error(f"Unexpected error: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST,
            "We couldn't process your request.",
        )


@router.post(
    "/cards/deactivate",
    status_code=status.HTTP_201_CREATED,
    response_model=SIMDeactivatedResponse,
)
@log_activity(stream_logs, ignored_params=("sim_service",))
@check_permissions
def sim_deactivate(
    request: Request,
    imsi: PushIMSI,
    sim_service: AbstractSimService = Depends(deps.sim_service),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> model.SIMDeactivatedResponse:
    """API for get SIM deactivate"""
    try:
        client_ip = get_client_ip(request)
        response = sim_service.suspend_sim(imsi.imsi, imsi.created_by, client_ip)
        anyio.from_thread.run(
            lambda: sim_events.sim_details_update([imsi.imsi], response.status)
        )
        return response
    except Unauthorized as e:
        logger.error(f"Unauthorized.: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, detail="Unauthorized access."
        )
    except SimCardsNotFound as e:
        logger.error(f"Sim card not found.:{str(e)}")
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except SimActivityError as e:
        logger.error(f"Sim activity error occoured.:{str(e)}")
        raise HTTPException(status_code=status.HTTP_409_CONFLICT, detail=str(e))
    except PplAuthError as e:
        logger.error(f"PPL auth error.:{str(e)}")
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail=str(e))
    except SimDeActivationError as e:
        logger.error(f"Sim deactivation error.:{str(e)}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except IMSINotAllocated as e:
        logger.error(f"IMSI not allocated.:{str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid IMSI.",
        )
    except (Exception, AssertionError) as e:
        logger.error(f"Unexpected error: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST,
            "We couldn't process your request.",
        )


@check_permissions
def audit_logs(
    request: Request,
    imsi: IMSI,
    month: Month,
    sim_service: AbstractSimService = Depends(deps.sim_service),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    pagination: Pagination = Depends(Pagination.query()),
    ordering: Ordering = Depends(
        Ordering.query(
            SIMCardAuditLogs,
            default_ordering="-createdDate",
            ordering_fields=(
                "imsi",
                "iccid",
                "msisdn",
                "requestType",
                "priorValue",
                "newValue",
                "field",
                "action",
                "clientIp",
                "createdBy",
                "createdDate",
            ),
        )
    ),
    searching: Searching
    | None = Depends(
        Searching.build(
            fields={
                "imsi",
                "iccid",
                "msisdn",
                "request_type",
                "prior_value",
                "new_value",
                "field",
                "action",
                "client_ip",
                "created_by",
            },
        )
    ),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> PaginatedResponse[SIMCardAuditLogs]:
    try:
        records, total_count = sim_service.audit_logs(
            imsi=imsi,
            month=month,
            pagination=pagination,
            ordering=ordering,
            searching=searching,
        )
        return PaginatedResponse.from_iterable(
            pagination=pagination,
            results=records,  # type: ignore
            total_count=total_count,
        )
    except Unauthorized as e:
        logger.error(f"Unauthorized.: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, detail="Unauthorized access."
        )
    except NoAuditLogs as e:
        logger.error(f"No audit logs found.:{str(e)}")
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except ValueError as e:
        logger.error(f"A value error occoured.:{str(e)}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except (Exception, AssertionError) as e:
        logger.error(f"Unexpected error: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST,
            "We couldn't process your request.",
        )


router.get(
    "/cards/audit",
    status_code=status.HTTP_200_OK,
    response_model=PaginatedResponse[SIMCardAuditLogs],
)(audit_logs)


@router.get(
    "/cards/summary/{imsi}",
    status_code=status.HTTP_200_OK,
    response_model=ConnectionSummary,
)
@check_permissions
def sim_connection_summary(
    request: Request,
    imsi: IMSI,
    sim_service: AbstractSimService = Depends(deps.sim_service),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> model.ConnectionSummary:
    try:
        return sim_service.get_connection_summary(IMSI(imsi))
    except IMSIDoesNotExit as e:
        logger.error(f"Summary endpoint error - IMSIDoesNotExsit: {str(e)}")
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        logger.error(f"Summary endpoint error - Exception: {str(e)}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@check_permissions
@measure_execution_time
def get_sims_usage(
    request: Request,
    account_id: int,
    sim_service: AbstractSimService = Depends(deps.sim_service),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    pagination: Pagination = Depends(Pagination.query()),
    ordering: Ordering = Depends(
        Ordering.query(
            SimUsage,
            default_ordering="iccid",
            ordering_fields=(
                "iccid",
                "msisdn",
                "imsi",
                "type",
                "allocationReference",
                "allocationDate",
                "simStatus",
                "ratePlan",
            ),
        )
    ),
    searching: Searching
    | None = Depends(
        Searching.build(
            fields={
                "iccid",
                "msisdn",
                "imsi",
                "_range.form_factor",
                "_allocation.title",
                "_allocation.created_at",
                "sim_status",
            },
        )
    ),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> PaginatedResponse[SimUsage]:
    try:
        records, total_count, summarize_values = sim_service.get_sim_usage(
            account_id=account_id,
            pagination=pagination,
            ordering=ordering,
            searching=searching,
        )
        return PaginatedResponse.from_iterable(
            pagination=pagination,
            results=map(SimUsage.from_model, records),
            total_count=total_count,
            summary=summarize_values,
        )
    except NoSimFound as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except NoTableExist as e:
        logger.warning(f"No Table exist: {str(e)}")
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"{str(e)}")
    except RatePlanNotFound as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)
        )
    except AssertionError as e:
        logger.error(f"Unexpected error: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST,
            "We couldn't process your request.",
        )
    except ForbiddenError as e:
        logger.error(f"Forbidden: {str(e)}")
        raise HTTPException(status.HTTP_403_FORBIDDEN, detail="Forbidden.")
    except (ValueError, ProgrammingError) as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST,
            "We couldn't process your request.",
        )


router.get("/cards/usage", response_model=PaginatedResponse[SimUsage])(get_sims_usage)


@check_permissions
@measure_execution_time
def get_sims_usage_export(
    request: Request,
    account_id: int,
    sim_service: AbstractSimService = Depends(deps.sim_service),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> Iterator[SimUsageExport]:
    try:
        records = sim_service.get_sim_usage_export(
            account_id=account_id,
        )

        return map(SimUsageExport.from_response_model, records)
    except NoSimFound as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except NoTableExist as e:
        logger.warning(f"No Table exist: {str(e)}")
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"{str(e)}")
    except RatePlanNotFound as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)
        )
    except AssertionError as e:
        logger.error(f"Unexpected error: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST,
            "We couldn't process your request.",
        )
    except ForbiddenError as e:
        logger.error(f"Forbidden: {str(e)}")
        raise HTTPException(status.HTTP_403_FORBIDDEN, detail="Forbidden.")
    except (ValueError, Exception) as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


router.get(
    "/cards/usage/export",
    summary="Export SIM cards to csv",
    response_class=CSVFileResponse,
)(convert_response_to_csv(SimUsageExport, "SimUsageExport")(get_sims_usage_export))


@router.get("/cards/active/statistic")
def get_active_sim_cards_statistic(
    account_id: int,
    month: Month,
    sim_service: AbstractSimService = Depends(deps.sim_service),
    pagination: Pagination = Depends(Pagination.query()),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> PaginatedResponse[ActiveSimMonthlyStatistic]:
    try:
        records, total_count = sim_service.cards_active_statistic(
            account_id=account_id, month=month, pagination=pagination
        )
        return PaginatedResponse.from_iterable(
            pagination=pagination,
            results=map(ActiveSimMonthlyStatistic.from_model, records),
            total_count=total_count,
        )
    except NoActiveSimFound as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@check_permissions
@measure_execution_time
def connection_history(
    request: Request,
    imsi: IMSI,
    month: Month,
    sim_service: AbstractSimService = Depends(deps.sim_service),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    pagination: Pagination = Depends(Pagination.query()),
    searching: Searching
    | None = Depends(
        Searching.build(
            fields={
                "iccid",
                "country",
                "carrier",
                "_cdrdata.session_starttime",
                "_cdrdata.session_endtime",
                "_cdrdata.duration",
                "_cdrdata.data_volume",
                "imsi",
                "country_name",
                "_carrier_name.carrier_name",
            },
        )
    ),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> PaginatedResponse[SimCDRHistory]:
    try:
        records, total_count = sim_service.connection_history(
            imsi=imsi,
            month=month,
            pagination=pagination,
            searching=searching,
        )
        return PaginatedResponse.from_iterable(
            pagination=pagination,
            results=map(SimCDRHistory.from_model, records),
            total_count=total_count,
        )
    except NoConnectionHistory as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except NoTableExist as e:
        logger.warning(f"No Table exist: {str(e)}")
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"{str(e)}")
    except IMSIDoesNotExit as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except (Exception, AssertionError) as e:
        logger.error(f"Unexpected error: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST,
            "We couldn't process your request.",
        )


router.get(
    "/connection/history",
    status_code=status.HTTP_200_OK,
    response_model=PaginatedResponse[SimCDRHistory],
)(connection_history)


@check_permissions
@measure_execution_time
def connection_history_export(
    request: Request,
    imsi: IMSI,
    month: Month,
    sim_service: AbstractSimService = Depends(deps.sim_service),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> Iterator[SimCDRHistory]:
    try:
        records = sim_service.connection_history_export(
            imsi=imsi,
            month=month,
        )
        return map(SimCDRHistory.from_model, records)
    except NoConnectionHistory as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except IMSIDoesNotExit as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except NoTableExist as e:
        logger.warning(f"No Table exist: {str(e)}")
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"{str(e)}")
    except IMSINotAllocated as e:
        logger.error((f"{str(e)}"))
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"Requested imsi {imsi} not allocated to the requested account.",
        )
    except (Exception, AssertionError) as e:
        logger.error(f"Unexpected error: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST,
            "We couldn't process your request.",
        )


router.get(
    "/connection/history/export",
    summary="Export  Connection History  to csv",
    response_class=CSVFileResponse,
)(
    convert_response_to_csv(SimCDRHistoryExport, "ConnetionHistory")(
        connection_history_export
    )
)


@router.get(
    "/cards/month/usage",
    status_code=status.HTTP_200_OK,
    response_model=MonthUsage,
)
@measure_execution_time
def month_usage(
    account_id: int | None = None,
    sim_service: AbstractSimService = Depends(deps.sim_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> model.MonthUsage:
    try:
        return sim_service.get_month_usage(account_id=account_id)
    except NoSimFound as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except NoTableExist as e:
        logger.warning(f"No Table exist: {str(e)}")
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"{str(e)}")
    except (Exception, AssertionError) as e:
        logger.error(f"Unexpected error: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST,
            "We couldn't process your request.",
        )


@router.get("/cards/{sim_status}", status_code=status.HTTP_200_OK)
def sim_status_details(
    sim_status: model.SimStatus,
    sim_service: AbstractSimService = Depends(deps.sim_service),
    pagination: Pagination = Depends(Pagination.query()),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> PaginatedResponse[SimStatusDetails]:
    """API for get SIM status wise count and details"""
    try:
        logger.info(
            f"/cards/{{sim_status}} sim_status : "
            f"{sim_status} . pagination : {pagination}"
        )
        records, total_count = sim_service.sim_status_details(
            sim_status=sim_status,
            pagination=pagination,
        )
        return PaginatedResponse.from_iterable(
            pagination=pagination,
            results=map(SimStatusDetails.from_model, records),
            total_count=total_count,
        )
    except NoSimCardsStatus as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except (Exception, AssertionError) as e:
        logger.error(f"Unexpected error: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST,
            "We couldn't process your request.",
        )


@router.patch(
    "/cards/status/{imsi}",
    status_code=status.HTTP_200_OK,
)
def update_sim_card(
    imsi: IMSI,
    request: Request,
    sim_service: AbstractSimService = Depends(deps.sim_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> bool:
    """API to get SIM work item status and Update by IMSI"""
    try:
        client_ip = get_client_ip(request)
        return sim_service.update_sim_card_by_imsi(imsi=imsi, client_ip=client_ip)
    except Unauthorized as e:
        logger.error(f"Unauthorized.: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, detail="Unauthorized access."
        )
    except PplAuthError as e:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail=str(e))
    except WorkItemIdNotFound as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except (Exception, AssertionError) as e:
        logger.error(f"We couldn't process your request.: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="We couldn't process your request.",
        )


@router.post(
    "/cards/bulk/{sim_status}",
    status_code=status.HTTP_202_ACCEPTED,
)
@log_activity(stream_logs, ignored_params=("sim_service",))
def sim_bulk_background_process(
    imsi_list: PushBulkIMSI,
    sim_status: model.SimStatus,
    request: Request,
    background_tasks: BackgroundTasks,
    sim_service: AbstractSimService = Depends(deps.sim_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> dict[str, str]:
    """API for get SIM bulk activate/deactivate"""
    try:
        allowed_statuses = [model.SimStatus.ACTIVE, model.SimStatus.DEACTIVATED]
        if sim_status not in allowed_statuses:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid sim_status value. Only Active/Deactivated are allowed.",
            )

        client_ip = get_client_ip(request)
        background_tasks.add_task(
            sim_service.bulk_background_process,
            imsi_list.imsi,
            imsi_list.created_by,
            sim_status,
            client_ip,
        )
        anyio.from_thread.run(
            lambda: sim_events.sim_details_update(
                imsi_list.imsi, model.SimStatus.PENDING
            )
        )
        return {"message": "We have received your request."}
    except BulkProcessingError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except SimError as e:
        logger.error(f"Sim bulk process error: {str(e)}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except IMSINotAllocated as e:
        logger.error(f"IMSI not allocated.:{str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid IMSI.",
        )
    except Exception as e:
        logger.error(f"We couldn't process your request.: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="We couldn't process your request.",
        )


@router.post("/copy/monthly-statistics", include_in_schema=True)
def copy_monthly_statistics_data(
    sim_service: AbstractSimService = Depends(deps.sim_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> dict:
    try:
        total = sim_service.copy_monthly_statistics()
        logger.info(f"Total number of rows affected : {total}")
        return {"Total records copied": total}
    except Exception as e:
        logger.error(f"message: {str(e)}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@router.get("/{iccids}", status_code=status.HTTP_200_OK)
def get_imsis(
    iccids: str,
    sim_service: AbstractSimService = Depends(deps.sim_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> Iterator[model.IMSIDetails]:
    try:
        iccids_list = iccids.split(",")
        return sim_service.get_imsis(iccids_list)  # type: ignore
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@check_permissions
@measure_execution_time
def voice_connection_history(
    request: Request,
    imsi: IMSI,
    month: Month,
    sim_service: AbstractSimService = Depends(deps.sim_service),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    pagination: Pagination = Depends(Pagination.query()),
    searching: Searching
    | None = Depends(
        Searching.build(
            fields={
                "iccid",
                "country",
                "carrier",
                "country_name",
                "_carrier_name.carrier_name",
                "_cdrvoice.call_date",
                "_cdrvoice.call_number",
                "_cdrvoice.call_minutes",
            },
        )
    ),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> PaginatedResponse[SimVoiceCDRHistory]:
    try:
        records, total_count = sim_service.voice_connection_history(
            imsi=imsi,
            month=month,
            pagination=pagination,
            searching=searching,
        )
        return PaginatedResponse.from_iterable(
            pagination=pagination,
            results=map(SimVoiceCDRHistory.from_model, records),
            total_count=total_count,
        )
    except NoConnectionHistory as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except NoTableExist as e:
        logger.warning(f"No Table exist: {str(e)}")
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"{str(e)}")
    except IMSIDoesNotExit as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except (Exception, AssertionError) as e:
        logger.error(f"Unexpected error: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST,
            "We couldn't process your request.",
        )


router.get(
    "/voice/connection/history",
    status_code=status.HTTP_200_OK,
    response_model=PaginatedResponse[SimVoiceCDRHistory],
)(voice_connection_history)


@check_permissions
@measure_execution_time
def voice_connection_history_export(
    request: Request,
    imsi: IMSI,
    month: Month,
    sim_service: AbstractSimService = Depends(deps.sim_service),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> Iterator[SimVoiceCDRHistory]:
    try:
        records = sim_service.voice_connection_history_export(
            imsi=imsi,
            month=month,
        )
        return map(SimVoiceCDRHistory.from_model, records)
    except NoConnectionHistory as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except IMSIDoesNotExit as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except NoTableExist as e:
        logger.warning(f"No Table exist: {str(e)}")
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"{str(e)}")
    except IMSINotAllocated as e:
        logger.error((f"{str(e)}"))
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"Requested imsi {imsi} not allocated to the requested account.",
        )
    except (Exception, AssertionError) as e:
        logger.error(f"Unexpected error: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST,
            "We couldn't process your request.",
        )


router.get(
    "/voice/connection/history/export",
    summary="Export voice Connection History  to csv",
    response_class=CSVFileResponse,
)(
    convert_response_to_csv(SimVoiceCDRHistoryExport, "VoiceConnectionHistory")(
        voice_connection_history_export
    )
)


@check_permissions
@measure_execution_time
def sms_connection_history(
    request: Request,
    imsi: IMSI,
    month: Month,
    sim_service: AbstractSimService = Depends(deps.sim_service),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    pagination: Pagination = Depends(Pagination.query()),
    searching: Searching
    | None = Depends(
        Searching.build(
            fields={
                "iccid",
                "country",
                "carrier",
                "country_name",
                "_carrier_name.carrier_name",
                "_cdrsms.sent_from",
                "_cdrsms.sent_to",
                "_cdrsms.date_sent",
            },
        )
    ),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> PaginatedResponse[SimSMSCDRHistory]:
    try:
        records, total_count = sim_service.sms_connection_history(
            imsi=imsi,
            month=month,
            pagination=pagination,
            searching=searching,
        )
        return PaginatedResponse.from_iterable(
            pagination=pagination,
            results=map(SimSMSCDRHistory.from_model, records),
            total_count=total_count,
        )
    except NoConnectionHistory as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except NoTableExist as e:
        logger.warning(f"No Table exist: {str(e)}")
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"{str(e)}")
    except IMSIDoesNotExit as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except (Exception, AssertionError) as e:
        logger.error(f"Unexpected error: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST,
            "We couldn't process your request.",
        )


router.get(
    "/sms/connection/history",
    status_code=status.HTTP_200_OK,
    response_model=PaginatedResponse[SimSMSCDRHistory],
)(sms_connection_history)


@check_permissions
@measure_execution_time
def sms_connection_history_export(
    request: Request,
    imsi: IMSI,
    month: Month,
    sim_service: AbstractSimService = Depends(deps.sim_service),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> Iterator[SimSMSCDRHistory]:
    try:
        records = sim_service.sms_connection_history_export(
            imsi=imsi,
            month=month,
        )
        return map(SimSMSCDRHistory.from_model, records)
    except NoConnectionHistory as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except IMSIDoesNotExit as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except NoTableExist as e:
        logger.warning(f"No Table exist: {str(e)}")
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"{str(e)}")
    except IMSINotAllocated as e:
        logger.error((f"{str(e)}"))
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"Requested imsi {imsi} not allocated to the requested account.",
        )
    except (Exception, AssertionError) as e:
        logger.error(f"Unexpected error: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST,
            "We couldn't process your request.",
        )


router.get(
    "/sms/connection/history/export",
    summary="Export sms Connection History  to csv",
    response_class=CSVFileResponse,
)(
    convert_response_to_csv(SimSMSCDRHistoryExport, "SMSConnectionHistory")(
        sms_connection_history_export
    )
)


@router.post(
    "/allocations/{allocation_reference}/account/{account_id}/"
    "rateplan/{rateplan_id}/custom",
    status_code=status.HTTP_201_CREATED,
)
def custom_allocations(
    request: Request,
    background_tasks: BackgroundTasks,
    form_data: UploadCustomIMSI = Depends(),
    redis_service: RedisService = Depends(redis_service),
    cdr_storage: CDRStorage = Depends(rdeps.custom_sim_storage),
    sim_service: AbstractSimService = Depends(deps.sim_service),
    streaming_service: AbstractKafkaAPI = Depends(get_streaming_service),
    account_service: AbstractAccountService = Depends(get_account_service_proxy),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> ResponseModel:
    """Custom Imsis allocation."""
    try:
        client_ip = get_client_ip(request)
        source_endpoint = request.scope["endpoint"].__name__.replace("_", " ").title()
        upload_status = partial(
            sim_service.add_upload_file_status,
            trace_id=trace_id,
            field=ALLOCATED,
            source_endpoint=source_endpoint,
            client_ip=client_ip,
            account_service=account_service,
        )
        upload_status()
        file_content = form_data.file.file.read()
        form_data.file.file.seek(0)
        cdr_storage.custom_sim_file(form_data.file.filename, file_content)
        allocation_result, valid_imsi = sim_service._validate_imsi_allocation(
            form_data=form_data,
            account_id=form_data.account_id,
            rate_plan_id=form_data.rate_plan_id,
            file_name=form_data.file.filename,
            file=form_data.file.file,
        )
        background_tasks.add_task(
            sim_service.custom_imsi_allocation,
            title=form_data.allocation_reference,
            account_id=form_data.account_id,
            rate_plan_id=form_data.rate_plan_id,
            sim_profile=form_data.sim_profile,
            msisdn_factor=form_data.msisdn_factor,
            file_name=form_data.file.filename,
            file=form_data.file.file,
            client_ip=client_ip,
            allocation_result=allocation_result,
            streaming_service=streaming_service,
            trace_id=trace_id,
            source_endpoint=source_endpoint,
            account_service=account_service,
            redis_service=redis_service,
            valid_imsi=valid_imsi,
        )
        return ResponseModel(message=REQUEST_MESSAGE, request_id=str(trace_id))
    except Unauthorized as e:
        logger.error(f"Unauthorized.: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, detail="Unauthorized access."
        )
    except SimLimitCountError as e:
        upload_status(message=str(e))
        logger.error(f"Sim Limit Count Error while allocating sims.: {str(e)}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=f"{str(e)}")
    except (
        SimError,
        ParsingError,
        NotValidFileName,
        FileSizeExceededLimit,
        RatePlanAccountMappingError,
        IMSINotAvailableForAllocation,
    ) as e:
        upload_status(message=str(e))
        logger.error(f"An error occoured while executing custom allocations.:{str(e)}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except RatePlanNotFound as e:
        upload_status(message=str(e))
        logger.error(f"Rateplan not found while executing custom allocations.:{str(e)}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Rate plan not found"
        )
    except RedisAPIError as e:
        logger.error(f"Error in custom_allocations. Error: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST,
            "An error occoured while allocating the sims.",
        )
    except ValueError as e:
        upload_status(message=str(e))
        logger.error(f"Value error occoured.: {str(e)}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except (Exception, AssertionError) as e:
        logger.error(f"Unexpected error: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST,
            "We couldn't process your request.",
        )


def execute_background_tasks(
    account_id: int,
    rate_plan_id: int,
    valid_sim: model.ReAllocationResult,
    client_ip: str,
    background_tasks: BackgroundTasks,
    sim_service: AbstractSimService,
    redis_service: RedisService,
):
    sim_service.re_allocation(
        account_id,
        rate_plan_id,
        valid_sim.valid_imsi_list,
        client_ip=client_ip,
        created_by="",
        same_account_imsi_list=valid_sim.same_account_imsi_list,
    )
    redis_service.allocate_sims(account_id, rate_plan_id)


@router.put(
    "/allocations/account/{account_id}/rateplan/{rate_plan_id}/reallocations",
    status_code=status.HTTP_202_ACCEPTED,
)
@check_permissions
def re_allocation_process(
    request: Request,
    account_id: int,
    rate_plan_id: int,
    imsi_list: ReAllocation,
    background_tasks: BackgroundTasks,
    sim_service: AbstractSimService = Depends(deps.sim_service),
    redis_service: RedisService = Depends(redis_service),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> dict:

    try:
        client_ip = get_client_ip(request)
        logger.error(f"Re_allocation_Re-Allocation request from {client_ip}")
        valid_sim, prev_rp_ids = sim_service.re_allocation_validation(
            account_id=account_id,
            rate_plan_id=rate_plan_id,
            imsi_list=imsi_list.imsi,
        )

        background_tasks.add_task(
            execute_background_tasks,
            account_id,
            rate_plan_id,
            valid_sim,
            client_ip,
            background_tasks,
            sim_service,
            redis_service,
        )
        if len(imsi_list.imsi) > 1:
            return {"message": valid_sim.message}
        else:
            return {"message": valid_sim.message, "privoiusPlanId": prev_rp_ids[0]}
    except ReAllocationError as e:
        logger.error(f"Sim Reallocation Error while re-allocating sims.: {str(e)}")
        raise HTTPException(status_code=status.HTTP_409_CONFLICT, detail=f"{str(e)}")
    except SimLimitCountError as e:
        logger.error(f"Sim Limit Count Error while re-allocating sims.: {str(e)}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=f"{str(e)}")
    except (RatePlanNotFound, IMSINotAllocated) as e:
        logger.error(f"Re_allocation_Re-Allocation error - {str(e)}")
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Unauthorized as e:
        logger.error(f"Unauthorized.: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, detail="Unauthorized access."
        )
    except (
        RatePlanAccountMappingError,
        AllocationError,
        IMSINotAvailableForAllocation,
    ) as e:
        logger.error(f"Re_allocation_Re-Allocation error - {str(e)}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except IMSIWiseError as e:
        logger.error(f"Re-alloction database error - {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="An error occoured while executing reallocation.",
        )
    except (IMSINotBelongToSameAccount, ReAllocationToSameAccount) as e:
        logger.error(f"Re_allocation_Re-Allocation error - {str(e)}")
        raise HTTPException(status_code=status.HTTP_409_CONFLICT, detail=str(e))
    except ReAllocationCountError as e:
        logger.error(f"Re_allocation_Re-Allocation Count error - {str(e)}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except (Exception, AssertionError) as e:
        logger.error(f"Re_allocation_Re-Allocation error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="We couldn't process your request.",
        )


@router.post("/update-msisdn", status_code=status.HTTP_200_OK)
async def update_msisdn(
    file: UploadFile = File(...),
    sim_service: AbstractSimService = Depends(deps.sim_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> bool:
    file_content = await file.read()

    try:
        data = parse_excel_file(file=file, file_stream=file_content)
        return sim_service.update_msisdn(excel_data=data)
    except ParsingError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except MSISDNExitError as e:
        logger.error(f"Error during MSISDN update: {str(e)}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except NoSimFound as e:
        logger.error(f"IMSI not found: {str(e)}")
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))


@router.get(
    "/account/{account_id}/rateplan/{rate_plan_id}/imsi/{imsi}/validate",
    status_code=status.HTTP_200_OK,
)
def validate_rate_plan_change(
    request: Request,
    account_id: int,
    rate_plan_id: int,
    imsi: IMSI,
    sim_service: AbstractSimService = Depends(deps.sim_service),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> model.RatePlanChangeSimLimitResult:
    try:
        valid_sim = sim_service.rate_plan_change_sim_validation(
            account_id=account_id,
            rate_plan_id=rate_plan_id,
            imsi=imsi,
        )

        return valid_sim

    except RatePlanAccountMappingError as e:
        logger.error(f"Rate Plan change validation - {str(e)}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except RatePlanChangeNotAllowed as e:
        logger.error(f"Rate Plan change validation - {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_412_PRECONDITION_FAILED, detail=str(e)
        )
    except RatePlanNotFound as e:
        logger.error(f"Rate Plan change validation - {str(e)}")
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except RatePlanException as e:
        logger.error(f"Rate Plan change validation - {str(e)}")
        raise HTTPException(status_code=status.HTTP_409_CONFLICT, detail=str(e))
    except SimCardsNotFound as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error(f"Rate Plan change validation - {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Couldn't process the request",
        )


@measure_execution_time
@router.get("/msisdn/factor/{msisdn_factor}", status_code=status.HTTP_200_OK)
def get_msisdn_factor(
    msisdn_factor: model.MSISDNFactor,
    sim_service: AbstractSimService = Depends(deps.sim_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> dict[str, str]:
    """API to Get Free MSISDN based on SIM Profile"""
    try:
        msisdn = sim_service.get_msisdn_factor(msisdn_factor)
        return {"result": msisdn}
    except NotFound as e:
        logger.error(f"Not found error occoured: {str(e)}")
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        logger.error(f"Couldn't process you request: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Couldn't process you request.",
        )


@router.patch(
    "/imsi/{imsi}/profile/{sim_profile}/msisdn/{msisdn}",
    status_code=status.HTTP_200_OK,
)
@measure_execution_time
@check_permissions
def update_sim_card_details(
    request: Request,
    background_tasks: BackgroundTasks,
    imsi: IMSI,
    sim_profile: model.SimProfile,
    msisdn: MSISDN,
    sim_service: AbstractSimService = Depends(deps.sim_service),
    streaming_service: AbstractKafkaAPI = Depends(get_streaming_service),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> UpdateSimCardDetailsResult:
    """API to Update MSISDN or SIM Profile"""
    try:
        client_ip = get_client_ip(request)
        response = sim_service.update_sim_card_details_by_imsi(
            imsi=imsi,
            msisdn=msisdn,
            sim_profile=sim_profile,
            client_ip=client_ip,
            streaming_service=streaming_service,
            background_tasks=background_tasks,
        )
        return UpdateSimCardDetailsResult.from_response_model(
            sim_msisdn_details=response
        )
    except (SimCardsNotFound, AllocationError) as e:
        logger.error(f"Not found error occoured.:{str(e)}")
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except MSISDNNotFound as e:
        logger.error(f"Msisdn Not Found.: {str(e)}")
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"{str(e)}")
    except IMSIError as e:
        logger.error(f"Imsi Error.: {str(e)}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=f"{str(e)}")
    except AlreadyExist as e:
        logger.error(f"Already Exist Error.: {str(e)}")
        raise HTTPException(status_code=status.HTTP_409_CONFLICT, detail=f"{str(e)}")
    except IMSINotAllocated as e:
        logger.error((f"{str(e)}"))
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Invalid IMSI.",
        )
    except Exception as e:
        logger.error(f"Couldn't process you request: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Couldn't process you request.",
        )
    except AuthException as e:
        logger.error(f"Error during MSISDN upload: {str(e)}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@router.get(
    "/msisdn/records",
    status_code=status.HTTP_200_OK,
    response_model=PaginatedResponse[MsisdnPool],
)
@measure_execution_time
@check_permissions
def get_msisdn_pool_details(
    request: Request,
    sim_service: AbstractSimService = Depends(deps.sim_service),
    pagination: Pagination = Depends(Pagination.query()),
    # ordering: Ordering = Depends(
    #     Ordering.query(
    #         MsisdnPool,
    #         default_ordering="-created_at",
    #         ordering_fields=(
    #             "msisdn",
    #             "simProfile",
    #             "msisdnFactor",
    #             "created_at",
    #         ),
    #     )
    # ),
    searching: Searching
    | None = Depends(
        Searching.build(
            fields={
                "msisdn",
                "sim_profile",
                "msisdn_factor",
                "uploaded_by",
            },
        )
    ),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> PaginatedResponse[MsisdnPool]:

    try:
        pool_msisdn, total_count = sim_service.get_msisdn_pool_details(
            pagination=pagination, searching=searching
        )
        return PaginatedResponse.from_iterable(
            pagination=pagination,
            results=list(map(MsisdnPool.from_model, pool_msisdn)),
            total_count=total_count,
        )
    except NotFound as e:
        logger.error(f"Msisdn Not Found: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="No Msisdn found."
        )
    except InvalidPage as e:
        logger.error(f"Invalid page error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"{str(e)}",
        )
    except Exception as e:
        logger.error(f"Couldn't process you request: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Couldn't process you request.",
        )
    except AuthException as e:
        logger.error(f"Error during MSISDN upload: {str(e)}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@measure_execution_time
@check_permissions
def get_msisdn_export(
    request: Request,
    sim_service: AbstractSimService = Depends(deps.sim_service),
    searching: Searching
    | None = Depends(
        Searching.build(
            fields={
                "msisdn",
                "sim_profile",
                "created_at",
                "uploaded_by",
            },
        )
    ),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> Iterator[MsisdnPoolExport]:

    try:
        pool_msisdn = sim_service.get_msisdn_export(searching=searching)

        return map(MsisdnPoolExport.from_response_model, pool_msisdn)
    except NotFound as e:
        logger.error(f"Failed to export msisdn not found error occoured:- {str(e)}")
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except ValueError as e:
        logger.error(f"Failed to export msisdn value error occoured:- {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)
        )
    except Exception as e:
        logger.error(f"Failed to export msisdn:- {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Couldn't process the request",
        )
    except AuthException as e:
        logger.error(f"Error during MSISDN upload: {str(e)}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


router.get(
    "/msisdn/export",
    summary="Export MSISDN List",
    response_class=CSVFileResponse,
)(convert_response_to_csv(MsisdnPoolExport, "SIMMsisdnPoolExport")(get_msisdn_export))


@router.get(
    "/msisdn/count",
    status_code=status.HTTP_200_OK,
    response_model=MsisdnCountDetails,
)
@check_permissions
@measure_execution_time
def get_available_msisdn_count(
    request: Request,
    sim_service: AbstractSimService = Depends(deps.sim_service),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> MsisdnCountDetails:

    try:
        available_msisdn_count_details = sim_service.get_available_msisdn_count()
        return MsisdnCountDetails.from_response_model(available_msisdn_count_details)
    except NotFound as e:
        logger.error(f"Failed to export msisdn not found error occoured:- {str(e)}")
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except ValueError as e:
        logger.error(f"Failed to export msisdn value error occoured:- {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)
        )
    except Exception as e:
        logger.error(f"Failed to get available msisdn count - {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Couldn't process the request",
        )
    except AuthException as e:
        logger.error(f"Error during MSISDN upload: {str(e)}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@router.post("/msisdn/documents", status_code=status.HTTP_201_CREATED)
@check_permissions
@measure_execution_time
def upload_msisdn(
    request: Request,
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    sim_service: AbstractSimService = Depends(deps.sim_service),
    cdr_storage: CDRStorage = Depends(rdeps.msisdn_pool_storage),
    streaming_service: AbstractKafkaAPI = Depends(get_streaming_service),
    account_service: AbstractAccountService = Depends(get_account_service_proxy),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> ResponseModel:
    try:
        source_endpoint = request.scope["endpoint"].__name__.replace("_", " ").title()
        client_ip = get_client_ip(request)
        upload_status = partial(
            sim_service.add_upload_file_status,
            trace_id=trace_id,
            field=MSISDN_FIELD,
            source_endpoint=source_endpoint,
            client_ip=client_ip,
            account_service=account_service,
        )
        upload_status()
        file_content = file.file.read()
        cdr_storage.custom_sim_file(file.filename, file_content)
        msisdn_list, invalid_msisdn, duplicate_msisdn, total = parse_msisdn_file(
            file=file, file_stream=file_content
        )
        background_tasks.add_task(
            sim_service.upload_msisdn,
            msisdn_list=msisdn_list,
            invalid_format=invalid_msisdn,
            duplicate_msisdn=duplicate_msisdn,
            client_ip=client_ip,
            streaming_service=streaming_service,
            trace_id=trace_id,
            source_endpoint=source_endpoint,
            account_service=account_service,
        )
        return ResponseModel(message=REQUEST_MESSAGE, request_id=str(trace_id))

    except ParsingError as e:
        upload_status(message=str(e))
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )
    except ValueError as e:
        upload_status(message=str(e))
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )
    except Exception as e:
        logger.error(f"Failed to get unallocated count - {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Couldn't process the request",
        )
    except AuthException as e:
        logger.error(f"Error during MSISDN upload: {str(e)}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@router.put(
    "/msisdn/profile/{sim_profile}/factor/{msisdn_factor}/documents",
    status_code=status.HTTP_200_OK,
)
@check_permissions
@measure_execution_time
def bulk_update_sim_card_details(
    request: Request,
    sim_profile: model.SimProfile,
    msisdn_factor: model.MSISDNFactor,
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    sim_service: AbstractSimService = Depends(deps.sim_service),
    cdr_storage: CDRStorage = Depends(rdeps.sim_msisdn_bulk_update_storage),
    streaming_service: AbstractKafkaAPI = Depends(get_streaming_service),
    account_service: AbstractAccountService = Depends(get_account_service_proxy),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> ResponseModel:
    try:
        source_endpoint = request.scope["endpoint"].__name__.replace("_", " ").title()
        client_ip = get_client_ip(request)
        upload_status = partial(
            sim_service.add_upload_file_status,
            trace_id=trace_id,
            field=MSISDN_FIELD,
            source_endpoint=source_endpoint,
            client_ip=client_ip,
            account_service=account_service,
        )
        upload_status()
        if (msisdn_factor == model.MSISDNFactor.INTERNATIONAL) and (
            sim_profile == model.SimProfile.VOICE_SMS_DATA
        ):
            raise ValueError(
                "sim_profile cannot be VOICE_SMS_DATA when "
                "msisdn_factor is INTERNATIONAL."
            )
        file_content = file.file.read()
        cdr_storage.custom_sim_file(file.filename, file_content)
        (
            total_records,
            invalid_records,
            duplicate_imsi,
            duplicate_msisdn,
            valid_imsi_list,
            valid_msisdn_list,
            valid_data,
        ) = parse_imsi_msisdn_csv(file=file, file_stream=file_content)
        sim_service.validate_common_request(valid_msisdn_list)
        background_tasks.add_task(
            sim_service.bulk_update_sim_card_details,
            total_records=total_records,
            sim_profile=sim_profile,
            msisdn_factor=msisdn_factor,
            invalid_records=invalid_records,
            duplicate_imsi=duplicate_imsi,
            duplicate_msisdn=duplicate_msisdn,
            valid_imsi_list=valid_imsi_list,
            valid_msisdn_list=valid_msisdn_list,
            valid_data=valid_data,
            client_ip=client_ip,
            streaming_service=streaming_service,
            trace_id=trace_id,
            source_endpoint=source_endpoint,
            account_service=account_service,
        )
        return ResponseModel(message=REQUEST_MESSAGE, request_id=str(trace_id))
    except (ParsingError, ValueError, IMSIError) as e:
        upload_status(message=str(e))
        logger.error((f"{str(e)}"))
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )
    except IMSINotAllocated as e:
        upload_status(message=str(e))
        logger.error((f"{str(e)}"))
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=(
                "Some of the requested imsis are not allocated to "
                "the specified account. Please verify."
            ),
        )
    except AuthException as e:
        logger.error(f"Error during MSISDN upload: {str(e)}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error(f"Failed to bulk update msisdn - {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Couldn't process the request",
        )


@router.put(
    "/msisdn",
    status_code=status.HTTP_200_OK,
)
@check_permissions
@measure_execution_time
def update_selected_sim_details(
    request: Request,
    update_request: BulkUpdateRequest,
    background_tasks: BackgroundTasks,
    sim_service: AbstractSimService = Depends(deps.sim_service),
    streaming_service: AbstractKafkaAPI = Depends(get_streaming_service),
    account_service: AbstractAccountService = Depends(get_account_service_proxy),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> ResponseModel:
    try:
        source_endpoint = request.scope["endpoint"].__name__.replace("_", " ").title()
        client_ip = get_client_ip(request)
        upload_status = partial(
            sim_service.add_upload_file_status,
            trace_id=trace_id,
            field=MSISDN_FIELD,
            source_endpoint=source_endpoint,
            client_ip=client_ip,
            account_service=account_service,
        )
        upload_status()
        imsi_list = [imsi.IMSI for imsi in update_request.msisdn_map]
        valid_data = [data.dict() for data in update_request.msisdn_map]
        background_tasks.add_task(
            sim_service.update_sim_card_details,
            total_records=len(update_request.msisdn_map),
            client_ip=client_ip,
            msisdn_factor=update_request.msisdn_factor,
            sim_profile=update_request.sim_profile,
            imsi_list=imsi_list,
            valid_data=valid_data,
            streaming_service=streaming_service,
            trace_id=trace_id,
            source_endpoint=source_endpoint,
            account_service=account_service,
        )
        return ResponseModel(message=REQUEST_MESSAGE, request_id=str(trace_id))
    except IMSIError as e:
        upload_status(status=UploadFileStatus.FAILED, message=str(e))
        logger.error(f"Imsi Error.: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )
    except MSISDNNotFound as e:
        upload_status(status=UploadFileStatus.FAILED, message=str(e))
        logger.error(f"Msisdn Not Found.: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e),
        )
    except Unauthorized as e:
        logger.error(f"Unauthorized.: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, detail="Unauthorized access."
        )
    except Exception as e:
        logger.error(f"Couldn't process you request: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Couldn't process you request.",
        )


@router.put(
    "/unallocation",
    status_code=status.HTTP_200_OK,
    response_model=UnallocateSimCardDetails,
)
def unallocate_imsis(
    unallocate_imsis: model.Unallocation,
    sim_service: AbstractSimService = Depends(deps.sim_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> UnallocateSimCardDetails:
    try:
        response = sim_service.unallocate_sim_cards(imsi_list=unallocate_imsis.imsis)
        return UnallocateSimCardDetails.from_response_model(unallocated_imsi=response)
    except UnallocationException as e:
        logger.error((f"{str(e)}"))
        raise HTTPException(status.HTTP_409_CONFLICT, detail=str(e))
    except ForbiddenError as e:
        logger.error((f"{str(e)}"))
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied.",
        )
    except Exception as e:
        logging.error(f"Error {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="We couldn't process your request.",
        )


@router.delete(
    "/imsi",
    status_code=status.HTTP_202_ACCEPTED,
)
@measure_execution_time
def delete_imsis(
    request: Request,
    sim_cards: model.ISMIToDelete,
    sim_service: AbstractSimService = Depends(deps.sim_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> IMSIDeleteResponse:
    try:
        response = sim_service.imsis_to_delete(sim_cards.imsis)
        return IMSIDeleteResponse(message=response.message)
    except ForbiddenError as e:
        logger.error((f"{str(e)}"))
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied.",
        )
    except ForbiddenError as e:
        logger.error((f"{str(e)}"))
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied.",
        )
    except Exception as e:
        logger.error(f"Couldn't process you request: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Couldn't process you request.",
        )


@router.post(
    "/cards/flush",
    status_code=status.HTTP_201_CREATED,
    response_model=SIMFlushResponse,
)
@log_activity(stream_logs, ignored_params=("sim_service",))
@check_permissions
def sim_flush(
    request: Request,
    imsi: PushIMSI,
    sim_service: AbstractSimService = Depends(deps.sim_service),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> model.SIMFlushResponse:
    """API for get SIM flush state"""
    try:
        client_ip = get_client_ip(request)
        return sim_service.flush_sim(
            imsi.imsi,
            imsi.created_by,
            client_ip,
        )
    except Unauthorized as e:
        logger.error(f"Unauthorized.: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, detail="Unauthorized access."
        )
    except SimCardsNotFound as e:
        logger.error(f"Sim card not found.:{str(e)}")
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except IMSINotAllocated as e:
        logger.error(f"IMSI not allocated.:{str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid IMSI.",
        )
    except AllocationError as e:
        logger.error(f"IMSI allocation not found.:{str(e)}")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Invalid IMSI.",
        )
    except SIMFlushError as e:
        logger.error(f"Unexpected error: {str(e)}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except SimError as e:
        logger.error(f"Sim profile not found.:{str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid IMSI Profile.",
        )
    except (Exception, AssertionError) as e:
        logger.error(f"Unexpected error: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST,
            "We couldn't process your request.",
        )


@router.post(
    "/cards/POD",
    status_code=status.HTTP_201_CREATED,
    response_model=SIMPODResponse,
)
@log_activity(stream_logs, ignored_params=("sim_service",))
@check_permissions
def sim_POD(
    request: Request,
    imsi: PushIMSI,
    sim_service: AbstractSimService = Depends(deps.sim_service),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> model.SIMPODResponse:
    """API for get SIM Disconnect"""
    try:
        client_ip = get_client_ip(request)
        return sim_service.pod_sim(
            imsi.imsi,
            imsi.created_by,
            client_ip,
        )
    except Unauthorized as e:
        logger.error(f"Unauthorized.: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, detail="Unauthorized access."
        )
    except SimCardsNotFound as e:
        logger.error(f"Sim card not found.:{str(e)}")
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except IMSINotAllocated as e:
        logger.error(f"IMSI not allocated.:{str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid IMSI.",
        )
    except AllocationError as e:
        logger.error(f"IMSI allocation not found.:{str(e)}")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Invalid IMSI.",
        )
    except (SIMActiveCallError, SIMPODError) as e:
        logger.error(f"Unexpected error: {str(e)}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except SimError as e:
        logger.error(f"Sim profile not found.:{str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid IMSI Profile.",
        )
    except (Exception, AssertionError) as e:
        logger.error(f"Unexpected error: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST,
            "We couldn't process your request.",
        )


@router.post(
    "/cards/SMS",
    status_code=status.HTTP_201_CREATED,
    response_model=SIMSendSMSResponse,
)
@log_activity(stream_logs, ignored_params=("sim_service",))
@check_permissions
def sim_SMS(
    request: Request,
    sms: PushSMS,
    sim_service: AbstractSimService = Depends(deps.sim_service),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> model.SIMSendSMSResponse:
    """API for send SMS to SIM"""
    try:
        client_ip = get_client_ip(request)

        return sim_service.sms_sim(
            sms.imsi,
            sms.message,
            sms.created_by,
            client_ip,
        )
    except Unauthorized as e:
        logger.error(f"Unauthorized.: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, detail="Unauthorized access."
        )
    except SimCardsNotFound as e:
        logger.error(f"Sim card not found.:{str(e)}")
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except IMSINotAllocated as e:
        logger.error(f"IMSI not allocated.:{str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid IMSI.",
        )
    except AllocationError as e:
        logger.error(f"IMSI allocation not found.:{str(e)}")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Invalid IMSI.",
        )
    except SIMSendSMSError as e:
        logger.error(f"Unexpected error: {str(e)}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except SimError as e:
        logger.error(f"Sim profile not found.:{str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid IMSI Profile.",
        )
    except (Exception, AssertionError) as e:
        logger.error(f"Unexpected error: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST,
            "We couldn't process your request.",
        )


@router.post(
    "/cards/APN",
    status_code=status.HTTP_201_CREATED,
    response_model=SIMAPNResponse,
)
@log_activity(stream_logs, ignored_params=("sim_service",))
@check_permissions
def sim_apn(
    request: Request,
    sim_apn_action: model.SIMAPNAction,
    sim: SIMAPN,
    sim_service: AbstractSimService = Depends(deps.sim_service),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> model.SIMAPNResponse:
    """API for update SIM APN"""
    try:
        client_ip = get_client_ip(request)
        return sim_service.apn_sim(
            sim.imsi,
            sim.apn_id,
            sim.created_by,
            sim_apn_action,
            client_ip,
        )
    except Unauthorized as e:
        logger.error(f"Unauthorized.: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, detail="Unauthorized access."
        )
    except SimCardsNotFound as e:
        logger.error(f"Sim card not found.:{str(e)}")
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except PplAuthError as e:
        logger.error(f"PPL auth error.:{str(e)}")
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail=str(e))
    except SimAPNError as e:
        logger.error(f"Sim APN update error.:{str(e)}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except IMSINotAllocated as e:
        logger.error(f"IMSI not allocated.:{str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid IMSI.",
        )
    except AllocationError as e:
        logger.error(f"IMSI allocation not found.:{str(e)}")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Invalid IMSI.",
        )
    except SimError as e:
        logger.error(f"Sim profile not found.:{str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid IMSI Profile.",
        )
    except (Exception, AssertionError) as e:
        logger.error(f"Unexpected error: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST,
            "We couldn't process your request.",
        )


@router.get(
    "/data-sessions/{imsi}/latest",
    response_model=DataSessionResponse,
    tags=["SIM Location"],
)
@check_permissions
def get_latest_data_session(
    request: Request,
    imsi: IMSI,
    sim_service: AbstractSimService = Depends(deps.sim_service),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> DataSessionResponse:
    """Get latest data session for a SIM by IMSI."""
    try:
        data_session = sim_service.get_latest_data_session(imsi=imsi)

        return DataSessionResponse.from_model(data_session)
    except SimAccountDataNotFound as e:
        logger.error(f"No sim data found error occurred: {e}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e),
        )
    except Exception as e:
        logger.error(f"Error getting data session for IMSI {imsi}: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to get data session for IMSI {imsi}: {str(e)}",
        )


@router.get(
    "/location/{imsi}/history",
    response_model=PaginatedResponse[LocationItemResponse],
    tags=["SIM Location"],
)
@check_permissions
def get_location(
    request: Request,
    imsi: IMSI,
    sim_service: AbstractSimService = Depends(deps.sim_service),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
    pagination: Pagination = Depends(Pagination.query()),
) -> PaginatedResponse[LocationItemResponse]:
    """Get location data for a SIM by IMSI."""
    try:
        # Pass account_service to service layer to handle IMSI-to-ICCID conversion
        location_response = sim_service.get_location(
            imsi=imsi,
            pagination=pagination,
        )

        # Convert to paginated response format
        location_items = [
            LocationItemResponse.from_model(item) for item in location_response.items
        ]

        return PaginatedResponse.from_iterable(
            pagination=pagination,
            results=location_items,
            total_count=location_response.meta.total_items,
        )
    except SimAccountDataNotFound as e:
        logger.error(f"No sim data found error occurred: {e}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e),
        )
    except Exception as e:
        logger.error(f"Error getting location for IMSI {imsi}: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to get location for IMSI {imsi}: {str(e)}",
        )


@router.get(
    "/location/{imsi}/latest",
    response_model=LocationItemResponse,
    tags=["SIM Location"],
)
@check_permissions
def get_latest_location(
    request: Request,
    imsi: IMSI,
    sim_service: AbstractSimService = Depends(deps.sim_service),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> LocationItemResponse:
    """Get latest location data for a SIM by IMSI."""
    try:
        # Pass account_service to service layer to handle IMSI-to-ICCID conversion
        latest_location = sim_service.get_latest_location(imsi=imsi)

        # Convert to response model
        return LocationItemResponse.from_model(latest_location)

    except SimAccountDataNotFound:
        logger.error(f"Sim account data not found for IMSI: {imsi}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"No latest location data found for IMSI {imsi}",
        )
    except Exception as e:
        logger.error(f"Error getting latest location for IMSI {imsi}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get(
    "/location/{imsi}/cell", response_model=CellLocationResponse, tags=["SIM Location"]
)
@check_permissions
def get_cell_location(
    request: Request,
    imsi: IMSI,
    sim_service: AbstractSimService = Depends(deps.sim_service),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> CellLocationResponse:
    """Get cell location data by cell tower information."""
    try:
        cell_location = sim_service.get_cell_location(imsi=imsi)
        return CellLocationResponse.from_model(cell_location)
    except SimAccountDataNotFound as e:
        logger.error(f"No cell location data found: {e}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e),
        )
    except Exception as e:
        logger.error(f"Error getting cell location: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to get cell location: {str(e)}",
        )


@router.get(
    "/{imsi}/subscriber/locate",
    status_code=status.HTTP_200_OK,
    response_model=SIMFetchMNCMCCResponse,
    tags=["SIM Location"],
)
@log_activity(stream_logs, ignored_params=("sim_service",))
@check_permissions
def fetch_mcc_mnc(
    request: Request,
    imsi: IMSI,
    sim_service: AbstractSimService = Depends(deps.sim_service),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> SIMFetchMNCMCCResponse:
    """API for fetching MNC (Mobile Network Code) and MCC (Mobile Country Code)"""
    try:
        client_ip = get_client_ip(request)
        return SIMFetchMNCMCCResponse.from_response_model(
            sim_service.fetch_mcc_mnc(
                imsi,
                client_ip=client_ip,
            )
        )
    except Unauthorized as e:
        logger.error(f"Unauthorized.: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, detail="Unauthorized access."
        )
    except SimCardsNotFound as e:
        logger.error(f"Sim card not found.:{str(e)}")
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except IMSIDoesNotExit as e:
        logger.error(f"IMSI not found.:{str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid ICCID.",
        )
    except SimFetchMNCMCCError as e:
        logger.error(str(e))
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )
    except PplAuthError as e:
        logger.error(f"PPL auth failed:{str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="PPL auth failed.",
        )
    except SimError as e:
        logger.error(f"Sim profile not found.:{str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid IMSI Profile.",
        )
    except HTTPError as e:
        logger.error(f"{str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )
    except Timeout as e:
        logger.error(f"{str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )
    except RequestException as e:
        logger.error(f"{str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )
    except RuntimeError as e:
        logger.error(f"{str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )
    except (Exception, AssertionError) as e:
        logger.error(f"Unexpected error: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST,
            "We couldn't process your request.",
        )
