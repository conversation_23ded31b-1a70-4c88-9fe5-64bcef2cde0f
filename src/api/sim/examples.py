_NR_MICRO_RANGE = {
    "id": 1,
    "title": "Reference",
    "provider": "NR",
    "formFactor": "MICRO",
    "quantity": 200,
    "imsiFirst": "***************",
    "imsiLast": "956473821000200",
    "remaining": 150,
    "createdAt": "2023-01-10T05:18:01",
    "createdBy": "<PERSON> Simmons",
}

_KYIVSTAR_NANO_RANGE = {
    "id": 2,
    "title": "Reference",
    "provider": "Kyivstar",
    "formFactor": "NANO",
    "quantity": 500,
    "imsiFirst": "***************",
    "imsiLast": "***************",
    "remaining": 120,
    "createdAt": "2022-12-30T11:55:13",
    "createdBy": "<PERSON>",
}

RANGES = [_NR_MICRO_RANGE, _KYIVSTAR_NANO_RANGE]

CREATE_RANGE_REQUEST = {"title": "Reference", "formFactor": "MICRO"}

CREATE_RANGE_RESPONSE = CREATE_RANGE_REQUEST | {
    "id": 1,
    "provider": "NR",
    "createdAt": "2023-01-12T17:50:10",
    "createdBy": "Brooklyn Simmons",
}

_KYIVSTAR_ALLOCATION = {
    "id": 1,
    "title": "Reference",
    "accountId": 1,
    "rangeId": 1,
    "quantity": 380,
    "imsiFirst": "***************",
    "imsiLast": "***************",
    "createdAt": "2022-12-31T11:55:13",
    "imsi": "***************",
}

_TESLA_ALLOCATION = {
    "id": 2,
    "title": "Reference",
    "accountId": 2,
    "rangeId": 2,
    "quantity": 50,
    "imsiFirst": "***************",
    "imsiLast": "***************",
    "createdAt": "2023-01-11T05:18:01",
    "imsi": "***************",
}

ALLOCATIONS = [_KYIVSTAR_ALLOCATION, _TESLA_ALLOCATION]


CREATE_ALLOCATION_REQUEST = {
    "title": "Reference",
    "accountId": 1,
    "rangeId": 1,
    "ratePlanId": 1,
    "quantity": 380,
}

SIM = {
    "id": 1,
    "rangeId": 1,
    "allocationId": 1,
    "ratePlanId": 1,
    "formFactor": "NANO",
    "iccid": "12345324342345234223",
    "imsi": "***************",
    "msisdn": "************",
    "status": "Unknown",
}

IMSI_RANGE_REQUEST = {"imsi_first": "**************", "length": 200}

NOTIFICATION_REQUEST = {
    "Results": [
        {
            "Name": "manxm2m",
            "Message": "Cease-OK",
            "Status": "SUCCESS",
            "WorkItemId": "9871352f-7f6b-4658-a435-837f7fd22513",
        },
        {"Name": "MAP", "Message": "Success", "Status": "SUCCESS"},
        {
            "Name": "PIP",
            "Message": "Ran 4 scripts without error from Close Customer",
            "Status": "SUCCESS",
        },
    ],
    "Reference": "************",
    "AuditDate": "2023-03-16T11:05:47.197",
    "RequestType": "CEASE",
    "Message": "Complete",
    "Status": "SUCCESS",
    "WorkItemId": "6412f71d549e801da0e421e9",
}


SIM_AUDIT_TRAIL_RESPONSE = {
    "id": "668fb2939c7e9160e5a9ef2d",
    "imsi": "***************",
    "iccid": "894453*************",
    "msisdn": "***************",
    "requestType": "PROVIDE",
    "priorValue": "Active",
    "newValue": "Active",
    "field": "Status",
    "action": "Updated",
    "clientIp": "127.0.0.1",
    "createdDate": "2024-07-11 15:53:15.214000",
    "createdBy": "<EMAIL>",
}

PROVIDER_LOG = {
    "activityId": "59363a8b-1de2-4afe-957b-9ad3ac84781e",
    "simActivityLogUuid": "89bbcf0e-626f-4815-ae7b-435ee8770808",
    "imsi": "***************",
    "iccid": "894453*************",
    "msisdn": "***************",
    "requestType": "str",
    "auditDate": "str",
    "message": "str",
    "status": "str",
    "workId": "str",
    "priorStatus": "str",
}


MSISDN_POOL_DETAILS = {
    "msisdn": "***************",
    "simProfile": "Voice only",
    "msisdnFactor": "NATIONAL",
    "createdAt": "2024-07-11 15:53:15.214000",
    "uoloadedBy": "<EMAIL>",
    "country": "United kingdom",
    "simProvider": "NR",
}

MSISDN_POOL = {
    **MSISDN_POOL_DETAILS,
    "id": 1,
    "accountName": "Microsoft",
    "logoKey": "/home/<USER>/tmp/monoglass/src/images/bt-logo.svg",
    "logoUrl": "https://example.com/icon",
}

FREE_MSISDN_COUNT_DETAILS = {"totalCount": 1502, "national": 502, "international": 1000}

SIM_MSISDN_COUNT_DETAILS = {
    "duplicateIccids": 5,
    "duplicateImsis": 2,
    "duplicateMsisdns": 1,
    "duplicateMsisdnPoolMsisdns": 1,
    "simCardMsisdnMissingMsisdnPool": 16,
    "simCardAllocationIdMismatchMsisdnPool": 9,
    "createRangeCount": 0,
    "addMsisdnPoolCount": 5,
    "updateSimMsisdnPoolCount": 1,
}

UPLOAD_MSISDN_RESULT = {"totalMsisdn": 10, "errorMsisdn": 0, "errorResult": []}

SIM_MSISDN_RESULT = {
    "imsi": "***************",
    "msisdn": "***************",
    "simProfile": "Voice only",
}

SIM_ACTIVATION_REQUEST = {
    "imsi": "***************",
    "createdBy": "string",
}

BULK_UPDATE_REQUEST = {
    "sim_profile": "VOICE_SMS_DATA",
    "msisdn_factor": "NATIONAL",
    "msisdn_map": [{"IMSI": "**************", "MSISDN": "125400000110512"}],
}


UNALLOCATED_SIM_CARDS = {"message": "0 out of 3 IMSIs were unallocated"}

ISMI_DELETE_MESSAGE = {"message": "Deleted 3 IMSIs; 1 IMSIs not found."}

SIM_SMS_REQUEST = {
    "imsi": "***************",
    "message": "ok",
    "createdBy": "<EMAIL>",
}


SIM_APN_REQUEST = {
    "imsi": "***************",
    "apnId": "01",
    "createdBy": "string",
}
