import logging

from fastapi import Depends
from sqlalchemy.orm import Session

from api.deps import get_authenticated_user
from api.deps import get_db_session as _get_db_session
from api.deps import get_mail_service_client
from auth.dto import AuthenticatedUser
from mail_delivery.service import AbstractMailService
from orders.adapters.repository import (
    AbstractOrdersRepository,
    DatabaseOrdersRepository,
)
from orders.proxies import OrdersServiceAuthProxy
from orders.services import AbstractOrdersService, OrdersService


def get_orders_repository(
    session: Session = Depends(_get_db_session),
) -> AbstractOrdersRepository:
    return DatabaseOrdersRepository(session)


def orders_service(
    orders_repository: AbstractOrdersRepository = Depends(get_orders_repository),
    mail_service: AbstractMailService = Depends(get_mail_service_client),
    authenticated_user: AuthenticatedUser = Depends(get_authenticated_user),
) -> AbstractOrdersService:
    logging.error("Calling deps service")
    return OrdersServiceAuthProxy(
        orders_service=OrdersService(
            orders_repository=orders_repository,
            mail_service=mail_service,
        ),
        user=authenticated_user,
    )
