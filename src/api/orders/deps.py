import logging

from fastapi import Depends
from sqlalchemy.orm import Session

from api.deps import get_auth_service, get_authenticated_user
from api.deps import get_db_session as _get_db_session
from api.deps import get_mail_service_client
from auth.dto import AuthenticatedUser
from mail_delivery.service import AbstractMailService
from orders.adapters.repository import (
    AbstractOrdersRepository,
    DatabaseOrdersRepository,
)
from functools import cache
from orders.proxies import OrdersServiceAuthProxy
from orders.services import AbstractOrdersService, OrdersService
from sim.adapters.externalapi import AuditServiceAPI
from sim.adapters.externalapi import FakeAuditServiceAPI
from auth.services import (
    AbstractAuthService,
    AccountAuthService,
    FakeAuthService,
    TokenIntrospectionAuthService,
)
from app.platform import get_auditlog_api_client
from collections.abc import Generator


def get_orders_repository(
    session: Session = Depends(_get_db_session),
) -> AbstractOrdersRepository:
    return DatabaseOrdersRepository(session)


@cache
def fake_audit_service_api():
    return FakeAuditServiceAPI()


def get_audit_service_client(
    authorization_service: AbstractAuthService = Depends(get_auth_service),
) -> Generator[AuditServiceAPI, None, None]:
    logging.error("Audit auth service")
    auth_token: str | None
    match authorization_service:
        case TokenIntrospectionAuthService(token=_token):
            auth_token = _token
        case AccountAuthService(
            auth_service=TokenIntrospectionAuthService(token=_token)
        ):
            auth_token = _token
        case AccountAuthService(auth_service=FakeAuthService()) | FakeAuthService():
            auth_token = None
        case _:
            raise AssertionError(
                f"Unexpected auth service: {type(authorization_service)}"
            )
    logging.error(f"Audit_auth token: {auth_token}")
    if auth_token:
        with get_auditlog_api_client(auth_token) as client:
            yield AuditServiceAPI(client)
    else:
        yield fake_audit_service_api()


def orders_service(
    orders_repository: AbstractOrdersRepository = Depends(get_orders_repository),
    mail_service: AbstractMailService = Depends(get_mail_service_client),
    audit_service: AuditServiceAPI = Depends(get_audit_service_client),
    authenticated_user: AuthenticatedUser = Depends(get_authenticated_user),
) -> AbstractOrdersService:
    logging.error("Calling deps service")
    return OrdersServiceAuthProxy(
        orders_service=OrdersService(
            orders_repository=orders_repository,
            mail_service=mail_service,
            audit_service=audit_service,
        ),
        user=authenticated_user,
    )
