import os
from typing import Iterator
from urllib.parse import unquote
from uuid import UUID, uuid4

from botocore.exceptions import ClientError, ParamValidationError, ValidationError
from defusedxml.ElementTree import fromstring
from fastapi import APIRouter, Depends, HTTPException, Request, Response
from fastapi_xml import XmlBody
from requests.exceptions import ConnectionError
from starlette import status

import api.deps as trace_id_deps
from api.cdrdata import deps
from api.decorators import measure_execution_time, validate_ip_address
from api.rating import deps as rdeps
from app.config import log_cdr_push, logger, settings
from cdrdata.domain import model
from cdrdata.exceptions import InvalidDate, NoTableExist
from cdrdata.services import AbstractCdrService
from common.exceptions import AnalyticsAPIError
from common.types import Month
from rating.storage_sync import CDRStorage

router = APIRouter()
cdrauth_router = APIRouter()


@router.post("/cdr/documents", status_code=status.HTTP_200_OK, include_in_schema=True)
@log_cdr_push(
    logger,
    ignored_params=(
        "cdr_service",
        "cdr_storage",
        "error_cdr_storage",
    ),
    sensitive_tags=(
        "CustomerReference",
        "SubscriberReference",
        "Subscriber",
        "Customer",
    ),
)
@validate_ip_address
@measure_execution_time
def real_time_usage_documents(
    request: Request,
    xml_string: str = XmlBody(),
    cdr_service: AbstractCdrService = Depends(deps.cdr_service),
    cdr_storage: CDRStorage = Depends(rdeps.cdr_storage),
    error_cdr_storage: CDRStorage = Depends(rdeps.error_cdr_storage),
    duplicate_cdr_storage: CDRStorage = Depends(rdeps.duplicate_cdr_storage),
) -> dict[str, UUID]:
    """Realtime cdr data store."""
    try:
        if not xml_string:
            raise ValueError("xml body cannot be empty.")
        root = fromstring(xml_string)
        if not root.tag == "cdr":
            raise ValueError("Invalid xml body.")
        s3_key = "temp_file_key"
        if not cdr_service.is_valid_xml_cdr(xml_string):
            if settings.USE_S3_STORAGE:
                s3_key = error_cdr_storage.error_cdr_file_storage(xml_string)
                logger.info(f"Invalid CDR found. CDR file_key: {s3_key}")
            return {"uuid": uuid4()}
        end_time = cdr_service.is_duplicate_cdr(xml_string)
        if end_time:
            if settings.USE_S3_STORAGE:
                s3_key = duplicate_cdr_storage.duplicate_cdr_file_storage(
                    xml_string, session_endtime_elem=end_time
                )
                logger.info(
                    f"Duplicate CDR found. Skipping DB entry. CDR filename: {s3_key}"
                )
            return {"uuid": uuid4()}
        if settings.USE_S3_STORAGE:
            s3_key = cdr_storage.real_time_usage_file(xml_string)
        updated_cdr = cdr_service.get_updated_cdr(s3_key=s3_key, xml_string=xml_string)
        cdr_object = cdr_service.add_cdr_to_analytics(updated_cdr)
        response = cdr_service.add_cdr(xml_string, cdr_object.cdr_object_id, s3_key)
        return {"uuid": response}
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)
        )
    except NoTableExist as e:
        logger.warning(f"No Table exist: {str(e)}")
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"{str(e)}")
    except (AnalyticsAPIError, ConnectionError) as e:
        logger.info(f"Error while calling analytics api. Error: {str(e)}")
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="We couldn't process your request.",
        )


@cdrauth_router.get("/cdr/duplicate")
@measure_execution_time
def get_duplicate_cdr(
    month: Month,
    cdr_service: AbstractCdrService = Depends(deps.cdr_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> Iterator[model.DuplicateCdr]:
    """_summary_

    Get the list of duplicate cdr within a month.\n
    month: `2024-04`
    """
    try:
        return cdr_service.get_duplicate_cdr(month)
    except InvalidDate as e:
        logger.error(str(e))
        raise HTTPException(status_code=400, detail=str(e))
    except NoTableExist as e:
        logger.warning(f"No Table exist: {str(e)}")
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"{str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="We couldn't process your request.",
        )


@cdrauth_router.get("/cdr/download", status_code=status.HTTP_200_OK)
def download_file_from_s3(
    file_key: str,
    cdr_storage: CDRStorage = Depends(rdeps.cdr_storage),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
):
    """_summary_

    file_key example: `2024-04-19/2024-04-19_4fab41d0-ccf1-4537-bfb5-086879493607.xml`
    """
    try:
        filename = os.path.basename(unquote(file_key))
        downloaded_path = cdr_storage.download_file_to_local(file_key)
        if not downloaded_path:
            raise FileNotFoundError
        with open(downloaded_path, mode="rb") as file:
            file_content = file.read()
        response = Response(content=file_content)
        response.headers["Content-Disposition"] = f"attachment; filename={filename}"
        os.remove(downloaded_path)
        return response
    except ClientError as e:
        logger.error(str(e))
        raise HTTPException(status_code=404, detail=str(e))
    except (ValidationError, ParamValidationError) as e:
        logger.error(str(e))
        raise HTTPException(status_code=400, detail=str(e))
    except FileNotFoundError as e:
        logger.error(str(e))
        raise HTTPException(status_code=404, detail="File not found")
    except Exception as e:
        logger.error(str(e))
        raise HTTPException(status_code=400, detail=str(e))
