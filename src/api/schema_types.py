from typing import Any, Generic, TypeVar

from pydantic import BaseModel as PydanticBaseModel

import common.pagination
import common.types


class CountryCode(common.types.CountryCode):
    @classmethod
    def __modify_schema__(cls, field_schema: dict[str, Any]) -> None:
        super().__modify_schema__(field_schema)
        # __modify_schema__ should mutate the dict it receives in place,
        # the returned value will be ignored
        field_schema.update(
            description="Alpha-2 country code",
            example="GB",
        )


class CurrencyCode(common.types.CurrencyCode):
    @classmethod
    def __modify_schema__(cls, field_schema: dict[str, Any]) -> None:
        super().__modify_schema__(field_schema)
        # __modify_schema__ should mutate the dict it receives in place,
        # the returned value will be ignored
        field_schema.update(
            description="Alpha-3 currency code",
            example="GBP",
        )


def to_camel(s: str) -> str:
    first, *others = s.split("_")
    return "".join([first.lower(), *map(str.title, others)])


class CamelBaseModel(PydanticBaseModel):
    class Config:
        json_encoders = {common.types.Month: str}
        alias_generator = to_camel
        allow_population_by_field_name = True


def to_human_readable(s: str) -> str:
    return " ".join(map(str.title, s.split("_")))


class HumanReadableBaseModel(PydanticBaseModel):
    class Config:
        alias_generator = to_human_readable
        allow_population_by_field_name = True


class IMSI(common.types.IMSI):
    @classmethod
    def __modify_schema__(cls, field_schema: dict[str, Any]) -> None:
        super().__modify_schema__(field_schema)
        # __modify_schema__ should mutate the dict it receives in place,
        # the returned value will be ignored
        field_schema.update(
            example="234588558795160",
        )


class ICCID(common.types.ICCID):
    @classmethod
    def __modify_schema__(cls, field_schema: dict[str, Any]) -> None:
        super().__modify_schema__(field_schema)
        field_schema.update(
            example="134588558795160",
        )


T = TypeVar("T")


class PaginatedResponse(
    CamelBaseModel, common.pagination.PaginatedResponse[T], Generic[T]
):
    ...
