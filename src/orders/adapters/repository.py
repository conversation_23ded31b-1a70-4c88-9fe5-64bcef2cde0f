from abc import ABC, abstractmethod
from datetime import datetime
from typing import List
from uuid import UUID

from more_itertools import ilen
from sqlalchemy import and_, asc, desc, func, nulls_first, nulls_last, select
from sqlalchemy.orm import Session, joinedload
from sqlalchemy.sql import Select

from api.orders.schemas import (
    TrackingInfo,
    UpdateOrderStatusRequest,
    UpdateOrderStatusResponse,
)
from app.config import logger
from common.ordering import Ordering
from common.pagination import Pagination
from common.parser import ParsingError
from common.searching import Searching, apply_search_to_sql
from orders.adapters import orm
from orders.constants import OrderStatus
from orders.domain import model
from sim.exceptions import NotFound


class AbstractOrdersRepository(ABC):
    @abstractmethod
    def create_order(self, order: model.OrderRequest) -> model.OrderResponse:
        ...

    @abstractmethod
    def add_order_customer(
        self, customer_details: model.OrderCustomer, order_id: UUID
    ) -> None:
        ...

    @abstractmethod
    def add_order_shipping(
        self, shipping_details: model.OrderShippingDetails, order_id: UUID
    ) -> None:
        ...

    @abstractmethod
    def add_order_item(
        self, order_items: List[model.OrderItem], order_id: UUID
    ) -> None:
        ...

    @abstractmethod
    def commit_order(self) -> None:
        ...

    @abstractmethod
    def update_order_status(
        self, order_id: UUID, update_data: UpdateOrderStatusRequest
    ) -> UpdateOrderStatusResponse:
        ...

    @abstractmethod
    def add_reject_reason(
        self,
        order_id: UUID,
        update_data: UpdateOrderStatusRequest,
    ) -> None:
        ...

    @abstractmethod
    def get_order(self, order_id: UUID) -> model.Order:
        ...

    @abstractmethod
    def get_orders(
        self,
        account_id: int | None = None,
        pagination: Pagination | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
    ) -> List[model.OrdersData]:
        ...

    @abstractmethod
    def get_orders_count(
        self,
        account_id: int | None = None,
        searching: Searching | None = None,
    ) -> int:
        ...

    @abstractmethod
    def get_order_details(self, order_id: UUID) -> model.OrderDetailsResponse:
        ...

    @abstractmethod
    def add_order_tracking(self, order_id: UUID, tracking: TrackingInfo) -> None:
        ...


class InMemoryOrdersRepository(AbstractOrdersRepository):
    def create_order(self, order: model.OrderRequest) -> model.OrderResponse:
        ...

    def add_order_customer(
        self, customer_details: model.OrderCustomer, order_id: UUID
    ) -> None:
        ...

    def add_order_shipping(
        self, shipping_details: model.OrderShippingDetails, order_id: UUID
    ) -> None:
        ...

    def add_order_item(
        self, order_items: List[model.OrderItem], order_id: UUID
    ) -> None:
        ...

    def commit_order(self) -> None:
        ...

    def update_order_status(
        self, order_id: UUID, update_data: UpdateOrderStatusRequest
    ) -> UpdateOrderStatusResponse:
        ...

    def add_reject_reason(
        self,
        order_id: UUID,
        update_data: UpdateOrderStatusRequest,
    ) -> None:
        ...

    def get_order(self, order_id: UUID) -> model.Order:
        ...

    def get_query(
        self,
        account_id: int | None = None,
        searching: Searching | None = None,
    ) -> Select:
        ...

    def get_orders(
        self,
        account_id: int | None = None,
        pagination: Pagination | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
    ) -> List[model.OrdersData]:
        ...

    def get_orders_count(
        self,
        account_id: int | None = None,
        searching: Searching | None = None,
    ) -> int:
        ...

    def get_order_details(self, order_id: UUID) -> model.OrderDetailsResponse:
        ...

    def add_order_tracking(self, order_id: UUID, tracking: TrackingInfo) -> None:
        ...


class DatabaseOrdersRepository(AbstractOrdersRepository):
    def __init__(self, session: Session) -> None:
        self.session = session
        self.condition_true = True

    def add_order_customer(
        self, customer_details: model.OrderCustomer, order_id: UUID
    ) -> None:
        customer_details.order_id = order_id
        self.session.add(customer_details)

    def add_order_shipping(
        self, shipping_details: model.OrderShippingDetails, order_id: UUID
    ) -> None:
        shipping_details.order_id = order_id
        self.session.add(shipping_details)

    def add_order_item(
        self, order_items: List[model.OrderItem], order_id: UUID
    ) -> None:
        self.session.add_all(
            model.OrderItem(
                order_id=order_id,
                sim_type=item.sim_type,
                quantity=item.quantity,
            )
            for item in order_items
        )

    def commit_order(self) -> None:
        try:
            self.session.commit()
        except Exception as e:
            logger.error(f"Failed to commit order: {e}")
            self.session.rollback()
            raise ParsingError("Failed to commit order, please try again.")

    def create_order(self, order: model.OrderRequest) -> model.OrderResponse:
        order_data = model.Order(
            order_by=order.order_by,
            order_date=datetime.now(),
            status=OrderStatus.PENDING.value,
        )
        self.session.add(order_data)
        self.session.flush()
        if not order_data.id:
            logger.error("Failed to create order, no response received.")
            self.session.rollback()
            raise ParsingError("Failed to create order, please try again.")
        return model.OrderResponse(id=order_data.uuid)

    def update_order_status(
        self, order_id: UUID, update_data: UpdateOrderStatusRequest
    ) -> UpdateOrderStatusResponse:
        self.session.execute(
            orm.orders.update()
            .where(orm.orders.c.uuid == order_id)
            .values(status=update_data.status)
        )
        self.session.commit()
        return UpdateOrderStatusResponse(id=order_id)

    def add_reject_reason(
        self,
        order_id: UUID,
        update_data: UpdateOrderStatusRequest,
    ) -> None:
        self.session.add(
            model.RejectReason(
                order_id=order_id,
                comment=update_data.comments,
            )
        )

    def get_query(
        self,
        account_id,
        searching: Searching | None = None,
    ) -> Select:

        query = (
            select(
                orm.orders.c.uuid,
                orm.orders.c.order_by,
                orm.orders.c.order_date,
                orm.orders.c.status,
                orm.customers.c.customer_account_name,
                orm.customers.c.person_placing_order,
                orm.customers.c.customer_account_logo_url,
                orm.customers.c.customer_email,
                orm.customers.c.customer_id,
                orm.customers.c.customer_account_id,
                (orm.customers.c.customer_contact_no.label("customer_phone")),
                func.json_agg(
                    func.json_build_object(
                        "sim_type",
                        model.OrderItem.sim_type,
                        "quantity",
                        orm.items.c.quantity,
                    )
                ).label("order_items"),
            )
            .join(orm.customers, orm.customers.c.order_id == orm.orders.c.uuid)
            .outerjoin(orm.items, orm.items.c.order_id == orm.orders.c.uuid)
            .group_by(
                orm.orders.c.uuid,
                orm.orders.c.order_by,
                orm.orders.c.order_date,
                orm.customers.c.customer_account_name,
                orm.customers.c.customer_id,
                orm.customers.c.customer_account_id,
                orm.customers.c.customer_account_logo_url,
                orm.customers.c.person_placing_order,
                orm.customers.c.customer_email,
                orm.customers.c.customer_contact_no,
                orm.orders.c.status,
            )
        )

        conditions = []

        if account_id is not None:
            conditions.append(orm.customers.c.customer_account_id == account_id)

        if conditions:
            query = query.where(and_(*conditions))
        if searching is not None:
            query = apply_search_to_sql(
                searching=searching, model=model.Order, stmt=query
            )
        return query

    def get_orders(
        self,
        account_id: int | None = None,
        pagination: Pagination | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
    ) -> List[model.OrdersData]:
        query = self.get_query(account_id=account_id, searching=searching)
        if ordering is not None:
            order_direction, null_order = (
                (desc, nulls_last)
                if ordering.order.lower() == "desc"
                else (asc, nulls_first)
            )

            fields_mapper = {
                "orderId": orm.orders.c.uuid,
                "orderDate": orm.orders.c.order_date,
                "status": orm.orders.c.status,
                "customerAccountName": orm.customers.c.customer_account_name,
                "personPlacingOrder": orm.customers.c.person_placing_order,
                "orderBy": orm.orders.c.order_by,
                "customerPhone": orm.customers.c.customer_contact_no,
                "accountId": orm.customers.c.customer_account_id,
                "customerId": orm.customers.c.customer_id,
                "customerEmail": orm.customers.c.customer_email,
            }

            if ordering.field in fields_mapper:
                order_field = fields_mapper[ordering.field]
            else:
                order_field = getattr(model.Order, ordering.field)

            query = query.order_by(null_order(order_direction(order_field))).order_by(
                order_direction(orm.orders.c.uuid)  # Ensure stable ordering by ID
            )
        if pagination:
            query = query.offset(pagination.offset).limit(pagination.page_size)

        rows = []

        for raw in self.session.execute(query).mappings():
            order_items = raw.get("order_items", "") if raw.get("order_items") else []
            items = sorted(order_items, key=lambda x: x["quantity"], reverse=True)
            rows.append(
                model.OrdersData(
                    order_id=raw["uuid"],
                    order_by=raw["order_by"],
                    customer_account_name=raw["customer_account_name"],
                    customer_account_logo_url=raw["customer_account_logo_url"],
                    person_placing_order=(
                        raw.get("person_placing_order", "")
                        if raw.get("person_placing_order")
                        else ""
                    ),
                    order_date=raw["order_date"],
                    customer_email=raw["customer_email"],
                    customer_phone=raw["customer_phone"],
                    status=raw["status"],
                    order_item=items,
                )
            )
        return rows

    def get_orders_count(
        self,
        account_id: int | None = None,
        searching: Searching | None = None,
    ) -> int:
        query = self.get_orders(
            account_id=account_id,
            searching=searching,
        )

        return ilen(query)

    def get_order_details(self, order_id: UUID) -> model.OrderDetailsResponse:
        order = (
            self.session.query(model.Order)
            .options(
                joinedload(getattr(model.Order, "customers")),
                joinedload(getattr(model.Order, "shipping_details")),
                joinedload(getattr(model.Order, "items")),
                joinedload(getattr(model.Order, "tracking")),
                joinedload(getattr(model.Order, "reject_reason")),
            )
            .filter(model.Order.uuid == order_id)
            .first()
        )

        if not order:
            raise NotFound("Data Not Found.")

        customer = order.customers[0] if order.customers else None
        shipping = order.shipping_details[0] if order.shipping_details else None
        tracking = order.tracking[0] if order.tracking else None
        comments = order.reject_reason[0].comment if order.reject_reason else None
        items = sorted(order.items, key=lambda x: x.quantity, reverse=True)
        # Build the response
        return model.OrderDetailsResponse(
            id=order.id,
            order_id=order.uuid,
            order_by=order.order_by,
            order_date=order.order_date,
            status=order.status,
            customer_details=customer,
            shipping_details=shipping,
            order_items=items,
            order_tracking=tracking,
            comments=comments,
        )

    def get_order(self, order_id: UUID) -> model.Order:
        query = select(model.Order).filter(model.Order.uuid == order_id)
        order = self.session.execute(query).scalar()
        if not order:
            raise NotFound("Data Not Found.")

        return order

    def add_order_tracking(self, order_id: UUID, tracking: TrackingInfo) -> None:

        order_tracking_data = model.OrderTracking(
            reference_id=tracking.reference_id,
            reference_url=tracking.reference_url,
            order_id=order_id,
        )
        self.session.add(order_tracking_data)
