from typing import BinaryIO, Iterable, Iterator, Type
from uuid import UUID

from fastapi import BackgroundTasks
from pandas import DataFrame

from accounts.services import AbstractAccountService
from api.sim.schemas import UploadCustomIMSI
from app.config import logger
from auth.dto import AuthenticatedUser
from auth.exceptions import ForbiddenError
from auth.permissions import is_distributor_client, is_distributor_staff
from common.ordering import Ordering
from common.pagination import Pagination
from common.parser import BaseCSVParser
from common.searching import Searching
from common.types import ICCID, IMSI, MSISDN, FormFactor, Month
from redis.service import AbstractRedisService
from sim.domain import model
from sim.domain.model import MSISDN as msisdn
from sim.domain.model import MarketShareData, MarketSharePeriod
from sim.exceptions import IMSINotAllocated
from sim.parser import ImsiCSVParser
from sim.services import AbstractSimService
from streaming.streaming import AbstractKafkaAPI


class SimServiceAuthProxy(AbstractSimService):
    def __init__(
        self,
        sim_service: AbstractSimService,
        user: AuthenticatedUser,
    ) -> None:
        self.sim_service = sim_service
        self.user = user

    def create_empty_range(
        self, title: str, form_factor: FormFactor, created_by: str
    ) -> model.Range:
        return self.sim_service.create_empty_range(title, form_factor, created_by)

    def create_range(
        self,
        title: str,
        form_factor: FormFactor,
        client_ip: str,
        sim_cards: list[model.SIMCard],
        trace_id: UUID,
        source_endpoint: str,
        account_service: AbstractAccountService,
        streaming_service: AbstractKafkaAPI,
        account_id: int | None = None,
        created_by: str | None = None,
    ) -> model.Range:
        created_by = self.user.email
        if is_distributor_client(self.user):
            logger.info("Distributor client")
            account_id = self.user.organization.account.id  # type: ignore
            logger.info(f"Distributor client account:{account_id}")
        return self.sim_service.create_range(
            title=title,
            form_factor=form_factor,
            client_ip=client_ip,
            sim_cards=sim_cards,
            trace_id=trace_id,
            source_endpoint=source_endpoint,
            account_service=account_service,
            streaming_service=streaming_service,
            created_by=created_by,
            account_id=account_id,
        )

    def _validate_imsi_range(self, sim_cards: list[model.SIMCard]) -> bool:
        return self.sim_service._validate_imsi_range(sim_cards)

    def remove_range(self, range_id: int) -> None:
        return self.sim_service.remove_range(range_id)

    def get_ranges(
        self,
        pagination: Pagination | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
    ) -> tuple[Iterator[model.Range], int]:
        return self.sim_service.get_ranges(
            pagination=pagination, ordering=ordering, searching=searching
        )

    def get_allocations(
        self, pagination: Pagination | None = None
    ) -> tuple[list[model.AllocationSummary], int]:
        return self.sim_service.get_allocations(pagination)

    def add_allocation(self, allocation: model.Allocation) -> model.Allocation:
        return self.sim_service.add_allocation(allocation)

    def remove_allocation(self, allocation_id: int) -> None:
        return self.sim_service.remove_allocation(allocation_id)

    def remove_allocations_by_range_id(self, range_id: int) -> None:
        return self.sim_service.remove_allocations_by_range_id(range_id)

    def get_sim_remains(self) -> Iterable[tuple[str, FormFactor, int]]:
        return self.sim_service.get_sim_remains()

    def get_sim_cards(
        self,
        account_id: int | None = None,
        *,
        rate_plan_ids: list[int] | None = None,
        pagination: Pagination | None = None,
        ordering: Ordering | None = None,
        imsi_list: list[IMSI] | None = None,
        creation_month: Month | None = None,
        searching: Searching | None = None,
    ) -> Iterator[model.SIMCard]:
        if is_distributor_staff(self.user):
            yield from self.sim_service.get_sim_cards(
                account_id=account_id,
                rate_plan_ids=rate_plan_ids,
                pagination=pagination,
                ordering=ordering,
                imsi_list=imsi_list,
                creation_month=creation_month,
                searching=searching,
            )
        elif is_distributor_client(self.user):
            # client query with account_id will be ignored
            # this method can be called by a client with another service
            yield from self.sim_service.get_sim_cards(
                account_id=self.user.organization.account.id,  # type: ignore
                rate_plan_ids=rate_plan_ids,
                pagination=pagination,
                ordering=ordering,
                imsi_list=imsi_list,
                creation_month=creation_month,
                searching=searching,
            )
        else:
            raise ForbiddenError

    def get_sim_cards_export(
        self,
        account_id: int | None = None,
        *,
        rate_plan_ids: list[int] | None = None,
        imsi_list: list[IMSI] | None = None,
        creation_month: Month | None = None,
    ) -> Iterator[model.SIMCard]:
        if is_distributor_staff(self.user):
            yield from self.sim_service.get_sim_cards_export(
                account_id=account_id,
                rate_plan_ids=rate_plan_ids,
                imsi_list=imsi_list,
                creation_month=creation_month,
            )
        elif is_distributor_client(self.user):

            yield from self.sim_service.get_sim_cards_export(
                account_id=self.user.organization.account.id,  # type: ignore
                rate_plan_ids=rate_plan_ids,
                imsi_list=imsi_list,
                creation_month=creation_month,
            )
        else:
            raise ForbiddenError

    def get_sim_count(
        self,
        account_id: int | None = None,
        *,
        rate_plan_ids: list[int] | None = None,
        creation_month: Month | None = None,
        imsi_list: list[IMSI] | None = None,
        searching: Searching | None = None,
        active_only: bool = False,
    ) -> int:
        if is_distributor_staff(self.user):
            return self.sim_service.get_sim_count(
                account_id=account_id,
                rate_plan_ids=rate_plan_ids,
                creation_month=creation_month,
                imsi_list=imsi_list,
                searching=searching,
                active_only=active_only,
            )
        elif is_distributor_client(self.user):
            return self.sim_service.get_sim_count(
                account_id=self.user.organization.account.id,  # type: ignore
                rate_plan_ids=rate_plan_ids,
                creation_month=creation_month,
                imsi_list=imsi_list,
                searching=searching,
                active_only=active_only,
            )
        else:
            raise ForbiddenError

    def sim_status(
        self, imsi: IMSI, created_by: str, client_ip: str | None = None
    ) -> model.SIMStatusResponse:
        created_by = self.user.email
        if is_distributor_staff(self.user):
            return self.sim_service.sim_status(imsi, created_by, client_ip)
        elif is_distributor_client(self.user):
            response = self.sim_service.check_imsi_account(
                account_id=self.user.organization.account.id,  # type: ignore
                imsi=[imsi],
            )
            if not response:
                raise IMSINotAllocated(
                    f"Requested imsi {imsi} not allocated to the requested account."
                )
            return self.sim_service.sim_status(imsi, created_by, client_ip)
        else:
            raise ForbiddenError

    def activate_sim(
        self,
        imsi: IMSI,
        created_by: str,
        client_ip: str | None = None,
    ):
        created_by = self.user.email
        if is_distributor_staff(self.user):
            return self.sim_service.activate_sim(imsi, created_by, client_ip)
        elif is_distributor_client(self.user):
            response = self.sim_service.check_imsi_account(
                account_id=self.user.organization.account.id,  # type: ignore
                imsi=[imsi],
            )
            if not response:
                logger.error(
                    f"Requested imsi {imsi} not allocated to the requested account."
                )
                raise IMSINotAllocated("Invalid IMSI.")
            return self.sim_service.activate_sim(imsi, created_by, client_ip)

    def suspend_sim(self, imsi: IMSI, created_by: str, client_ip: str | None = None):
        created_by = self.user.email
        if is_distributor_staff(self.user):
            return self.sim_service.suspend_sim(imsi, created_by, client_ip)
        elif is_distributor_client(self.user):
            response = self.sim_service.check_imsi_account(
                account_id=self.user.organization.account.id,  # type: ignore
                imsi=[imsi],
            )
            if not response:
                logger.error(
                    f"Requested imsi {imsi} not allocated to the requested account."
                )
                raise IMSINotAllocated("Invalid IMSI.")
            return self.sim_service.suspend_sim(imsi, created_by, client_ip)

    def audit_logs(
        self,
        imsi: IMSI,
        month: Month,
        is_client: bool | None = False,
        account_id: int | None = None,
        pagination: Pagination | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
    ):
        if is_distributor_staff(self.user):
            return self.sim_service.audit_logs(
                imsi=imsi,
                month=month,
                account_id=account_id,
                pagination=pagination,
                ordering=ordering,
                searching=searching,
            )
        elif is_distributor_client(self.user):
            return self.sim_service.audit_logs(
                imsi=imsi,
                month=month,
                is_client=True,
                account_id=self.user.organization.account.id,  # type: ignore
                pagination=pagination,
                ordering=ordering,
                searching=searching,
            )
        else:
            raise ForbiddenError

    def cards_active_statistic(
        self, account_id: int, month: Month, pagination: Pagination | None = None
    ):
        return self.sim_service.cards_active_statistic(account_id, month, pagination)

    def get_connection_summary(self, imsi: IMSI):
        return self.sim_service.get_connection_summary(imsi)

    def get_sim_usage(
        self,
        account_id: int,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
        pagination: Pagination | None = None,
    ):
        if is_distributor_staff(self.user):
            return self.sim_service.get_sim_usage(
                account_id=account_id,
                ordering=ordering,
                searching=searching,
                pagination=pagination,
            )
        elif is_distributor_client(self.user):
            # client query with account_id will be ignored
            # this method can be called by a client with another service
            if account_id != self.user.organization.account.id:  # type: ignore
                raise ForbiddenError
            return self.sim_service.get_sim_usage(
                account_id=self.user.organization.account.id,  # type: ignore
                ordering=ordering,
                searching=searching,
                pagination=pagination,
            )
        else:
            raise ForbiddenError

    def connection_history(
        self,
        imsi: IMSI,
        month: Month,
        account_id: int | None = None,
        pagination: Pagination | None = None,
        searching: Searching | None = None,
    ):
        if is_distributor_staff(self.user):
            return self.sim_service.connection_history(
                imsi=imsi,
                month=month,
                account_id=account_id,
                pagination=pagination,
                searching=searching,
            )
        elif is_distributor_client(self.user):
            return self.sim_service.connection_history(
                imsi=imsi,
                month=month,
                account_id=self.user.organization.account.id,  # type: ignore
                pagination=pagination,
                searching=searching,
            )
        else:
            raise ForbiddenError

    def get_sim_usage_export(
        self,
        account_id: int,
    ):
        if is_distributor_staff(self.user):
            return self.sim_service.get_sim_usage_export(account_id=account_id)
        elif is_distributor_client(self.user):
            # client query with account_id will be ignored
            # this method can be called by a client with another service
            if account_id != self.user.organization.account.id:  # type: ignore
                raise ForbiddenError
            return self.sim_service.get_sim_usage_export(
                account_id=self.user.organization.account.id,  # type: ignore
            )
        else:
            raise ForbiddenError

    def connection_history_export(
        self,
        imsi: IMSI,
        month: Month,
        account_id: int | None = None,
    ):
        if is_distributor_staff(self.user):
            return self.sim_service.connection_history_export(
                imsi=imsi,
                month=month,
                account_id=account_id,
            )
        elif is_distributor_client(self.user):
            account_id = self.user.organization.account.id  # type: ignore
            response = self.sim_service.check_imsi_account(
                account_id=account_id,
                imsi=[imsi],
            )
            if not response:
                raise IMSINotAllocated(
                    f"Requested imsi {imsi} not allocated to the requested account."
                )
            return self.sim_service.connection_history_export(imsi, month, account_id)
        else:
            raise ForbiddenError

    def get_month_usage(self, account_id: int | None = None):
        if is_distributor_staff(self.user):
            return self.sim_service.get_month_usage(account_id=account_id)
        elif is_distributor_client(self.user):
            return self.sim_service.get_month_usage(
                account_id=self.user.organization.account.id  # type: ignore
            )
        else:
            raise ForbiddenError

    def sim_status_details(
        self,
        sim_status: model.SimStatus,
        pagination: Pagination | None = None,
        account_id: int | None = None,
    ):
        if is_distributor_staff(self.user):
            return self.sim_service.sim_status_details(
                sim_status=sim_status, pagination=pagination, account_id=account_id
            )
        elif is_distributor_client(self.user):
            return self.sim_service.sim_status_details(
                sim_status=sim_status,
                pagination=pagination,
                account_id=self.user.organization.account.id,  # type: ignore
            )
        else:
            raise ForbiddenError

    def update_sim_card_by_imsi(
        self, imsi: IMSI, client_ip: str, created_by: str | None = None
    ) -> bool:
        created_by = self.user.email
        return self.sim_service.update_sim_card_by_imsi(imsi, client_ip, created_by)

    def process_notification(
        self,
        request_type: model.RequestType,
        sim_provider_log: model.SIMProviderLog,
        client_ip: str | None = None,
    ) -> bool:
        ...

    def bulk_background_process(
        self,
        imsi: list[IMSI],
        created_by: str,
        sim_status: model.SimStatus,
        client_ip: str | None = None,
    ) -> None:
        created_by = self.user.email
        if is_distributor_client(self.user):
            response = self.sim_service.check_imsi_account(
                account_id=self.user.organization.account.id,  # type: ignore
                imsi=imsi,
            )
            if not response:
                logger.error(
                    f"Requested imsi {imsi} not allocated to the requested account."
                )
                raise IMSINotAllocated("Invalid IMSI.")
        return self.sim_service.bulk_background_process(
            imsi, created_by, sim_status, client_ip
        )

    def copy_monthly_statistics(self) -> int:
        return self.sim_service.copy_monthly_statistics()

    def get_imsis(self, iccids: list[ICCID]) -> Iterator[model.IMSIDetails]:
        return self.sim_service.get_imsis(iccids)

    def get_market_share_by_account(
        self, market_share_data: MarketShareData
    ) -> model.MarketShareCarrier:
        logger.info(f"RequestUser {self.user}")
        if is_distributor_staff(self.user):
            return self.sim_service.get_market_share_by_account(market_share_data)
        elif is_distributor_client(self.user):
            account_id = self.user.organization.account.id  # type: ignore
            market_share_data.account_id = account_id
            return self.sim_service.get_market_share_by_account(market_share_data)
        else:
            raise ForbiddenError

    def get_market_share(self, period: MarketSharePeriod) -> model.MarketShareCarrier:
        logger.info(f"RequestUser {self.user}")
        if is_distributor_staff(self.user):
            return self.sim_service.get_market_share(period)
        elif is_distributor_client(self.user):
            account_id = self.user.organization.account.id  # type: ignore
            market_share_data = MarketShareData(
                account_id=account_id,
                from_date=period.from_date,
                to_date=period.to_date,
            )
            return self.sim_service.get_market_share_by_account(market_share_data)
        else:
            raise ForbiddenError

    def voice_connection_history(
        self,
        imsi: IMSI,
        month: Month,
        account_id: int | None = None,
        pagination: Pagination | None = None,
        searching: Searching | None = None,
    ):
        if is_distributor_staff(self.user):
            return self.sim_service.voice_connection_history(
                imsi=imsi,
                month=month,
                account_id=account_id,
                pagination=pagination,
                searching=searching,
            )
        elif is_distributor_client(self.user):
            return self.sim_service.voice_connection_history(
                imsi=imsi,
                month=month,
                account_id=self.user.organization.account.id,  # type: ignore
                pagination=pagination,
                searching=searching,
            )
        else:
            raise ForbiddenError

    def voice_connection_history_export(
        self,
        imsi: IMSI,
        month: Month,
        account_id: int | None = None,
    ):
        if is_distributor_staff(self.user):

            return self.sim_service.voice_connection_history_export(
                imsi=imsi,
                month=month,
                account_id=account_id,
            )
        elif is_distributor_client(self.user):
            account_id = self.user.organization.account.id  # type: ignore
            response = self.sim_service.check_imsi_account(
                account_id=account_id,
                imsi=[imsi],
            )
            if not response:
                raise IMSINotAllocated(
                    f"Requested imsi {imsi} not allocated to the requested account."
                )
            return self.sim_service.voice_connection_history_export(
                imsi, month, account_id
            )
        else:
            raise ForbiddenError

    def sms_connection_history(
        self,
        imsi: IMSI,
        month: Month,
        account_id: int | None = None,
        pagination: Pagination | None = None,
        searching: Searching | None = None,
    ):
        if is_distributor_staff(self.user):
            return self.sim_service.sms_connection_history(
                imsi=imsi,
                month=month,
                account_id=account_id,
                pagination=pagination,
                searching=searching,
            )
        elif is_distributor_client(self.user):
            return self.sim_service.sms_connection_history(
                imsi=imsi,
                month=month,
                account_id=self.user.organization.account.id,  # type: ignore
                pagination=pagination,
                searching=searching,
            )
        else:
            raise ForbiddenError

    def sms_connection_history_export(
        self,
        imsi: IMSI,
        month: Month,
        account_id: int | None = None,
    ):
        if is_distributor_staff(self.user):

            return self.sim_service.sms_connection_history_export(
                imsi=imsi,
                month=month,
                account_id=account_id,
            )
        elif is_distributor_client(self.user):
            account_id = self.user.organization.account.id  # type: ignore
            response = self.sim_service.check_imsi_account(
                account_id=account_id,
                imsi=[imsi],
            )
            if not response:
                raise IMSINotAllocated(
                    f"Requested imsi {imsi} not allocated to the requested account."
                )
            return self.sim_service.sms_connection_history_export(
                imsi, month, account_id
            )
        else:
            raise ForbiddenError

    def get_market_share_imsi(
        self, period: MarketSharePeriod, imsi: IMSI
    ) -> model.MarketShareCarrier:
        return self.sim_service.get_market_share_imsi(period, imsi)

    def custom_imsi_allocation(
        self,
        title: str,
        rate_plan_id: int,
        sim_profile: model.SimProfile,
        msisdn_factor: model.MSISDNFactor,
        file_name: str,
        file: BinaryIO,
        client_ip: str,
        allocation_result: model.AllocationResult,
        streaming_service: AbstractKafkaAPI,
        trace_id: UUID,
        source_endpoint: str,
        account_service: AbstractAccountService,
        redis_service: AbstractRedisService,
        valid_imsi: list[IMSI],
        account_id: int,
        parser_impl: Type[BaseCSVParser] = ImsiCSVParser,
        created_by: str | None = None,
    ) -> model.AllocationResult:
        created_by = self.user.email
        if is_distributor_client(self.user):
            logger.info("Distributor client")
            account_id = self.user.organization.account.id  # type: ignore
            logger.info(f"Distributor client account:{account_id}")
        if is_distributor_staff(self.user):
            return self.sim_service.custom_imsi_allocation(
                title=title,
                account_id=account_id,
                rate_plan_id=rate_plan_id,
                sim_profile=sim_profile,
                msisdn_factor=msisdn_factor,
                file_name=file_name,
                file=file,
                client_ip=client_ip,
                created_by=created_by,
                allocation_result=allocation_result,
                parser_impl=parser_impl,
                streaming_service=streaming_service,
                trace_id=trace_id,
                source_endpoint=source_endpoint,
                account_service=account_service,
                redis_service=redis_service,
                valid_imsi=valid_imsi,
            )
        else:
            raise ForbiddenError

    def _validate_imsi_allocation(
        self,
        form_data: UploadCustomIMSI,
        account_id: int,
        rate_plan_id: int,
        file_name: str,
        file: BinaryIO,
        parser_impl: Type[BaseCSVParser] = ImsiCSVParser,
    ) -> tuple[model.AllocationResult, list[IMSI]]:
        return self.sim_service._validate_imsi_allocation(
            form_data=form_data,
            account_id=account_id,
            rate_plan_id=rate_plan_id,
            file_name=file_name,
            file=file,
            parser_impl=parser_impl,
        )

    def check_imsi_account(
        self,
        account_id: int | None,
        imsi: list[IMSI],
    ) -> bool:
        return self.sim_service.check_imsi_account(account_id=account_id, imsi=imsi)

    def re_allocation(
        self,
        account_id: int,
        rate_plan_id: int,
        imsi_list: list[IMSI],
        client_ip: str,
        created_by: str,
        same_account_imsi_list: list[IMSI] | None = None,
    ) -> bool:
        created_by = self.user.email
        if is_distributor_staff(self.user):
            return self.sim_service.re_allocation(
                account_id=account_id,
                rate_plan_id=rate_plan_id,
                imsi_list=imsi_list,
                client_ip=client_ip,
                created_by=created_by,
                same_account_imsi_list=same_account_imsi_list,
            )
        elif is_distributor_client(self.user):
            account_id = self.user.organization.account.id  # type: ignore
            response = self.sim_service.check_imsi_account(
                account_id=account_id,
                imsi=imsi_list,
            )
            if not response:
                raise IMSINotAllocated(
                    f" imsi {imsi_list} not allocated to the requested account."
                )
            return self.sim_service.re_allocation(
                account_id=account_id,
                rate_plan_id=rate_plan_id,
                imsi_list=imsi_list,
                client_ip=client_ip,
                created_by=created_by,
                same_account_imsi_list=same_account_imsi_list,
            )
        else:
            raise ForbiddenError

    def re_allocation_validation(
        self,
        imsi_list: list[IMSI],
        account_id: int,
        rate_plan_id: int,
    ) -> tuple[model.ReAllocationResult, list[int]]:
        if is_distributor_staff(self.user):
            return self.sim_service.re_allocation_validation(
                imsi_list=imsi_list,
                account_id=account_id,
                rate_plan_id=rate_plan_id,
            )
        elif is_distributor_client(self.user):
            account_id = self.user.organization.account.id  # type: ignore
            response = self.sim_service.check_imsi_account(
                account_id=account_id,
                imsi=imsi_list,
            )
            if not response:
                raise IMSINotAllocated(
                    f"imsi {imsi_list} not allocated to the requested account."
                )
            return self.sim_service.re_allocation_validation(
                imsi_list=imsi_list,
                account_id=account_id,
                rate_plan_id=rate_plan_id,
            )
        else:
            raise ForbiddenError

    def update_msisdn(self, excel_data):
        return self.sim_service.update_msisdn(excel_data=excel_data)

    def rate_plan_change_sim_validation(
        self,
        imsi: IMSI,
        account_id: int,
        rate_plan_id: int,
    ) -> model.RatePlanChangeSimLimitResult:
        return self.sim_service.rate_plan_change_sim_validation(
            imsi=imsi,
            account_id=account_id,
            rate_plan_id=rate_plan_id,
        )

    def get_msisdn_factor(self, msisdn_factor: model.MSISDNFactor) -> str:
        return self.sim_service.get_msisdn_factor(msisdn_factor)

    def update_sim_card_details_by_imsi(
        self,
        imsi: IMSI,
        msisdn: MSISDN,
        sim_profile: model.SimProfile,
        streaming_service: AbstractKafkaAPI,
        background_tasks: BackgroundTasks,
        client_ip: str,
        created_by: str | None = None,
    ) -> model.UpdateSimCardDetailsResult:
        created_by = self.user.email
        if is_distributor_client(self.user):
            account_id = self.user.organization.account.id  # type: ignore
            response = self.sim_service.check_imsi_account(
                account_id=account_id,
                imsi=[imsi],
            )
            if not response:
                raise IMSINotAllocated(
                    f"imsi {imsi} not allocated to the requested account."
                )
        return self.sim_service.update_sim_card_details_by_imsi(
            imsi=imsi,
            msisdn=msisdn,
            sim_profile=sim_profile,
            streaming_service=streaming_service,
            background_tasks=background_tasks,
            client_ip=client_ip,
            created_by=created_by,
        )

    def get_msisdn_pool_details(
        self,
        pagination: Pagination | None = None,
        # ordering: Ordering | None = None,
        searching: Searching | None = None,
    ) -> tuple[list[model.MsisdnDetails], int]:
        return self.sim_service.get_msisdn_pool_details(
            pagination=pagination, searching=searching
        )

    def get_msisdn_export(
        self,
        searching: Searching | None = None,
    ) -> Iterator[model.MsisdnDetails]:
        return self.sim_service.get_msisdn_export(searching=searching)

    def get_available_msisdn_count(self) -> model.MsisdnCountDetails:
        return self.sim_service.get_available_msisdn_count()

    def upload_msisdn(
        self,
        msisdn_list: list[model.MSISDN],
        invalid_format: list[str],
        duplicate_msisdn: list[model.MSISDN],
        client_ip: str,
        trace_id: UUID,
        source_endpoint: str,
        streaming_service: AbstractKafkaAPI,
        account_service: AbstractAccountService,
        uploaded_by: str | None = None,
        account_id: int | None = None,
    ) -> model.MsisdnResult:
        uploaded_by = self.user.email
        if is_distributor_client(self.user):
            logger.info("Distributor client")
            account_id = self.user.organization.account.id  # type: ignore
            logger.info(f"Distributor client account:{account_id}")
        return self.sim_service.upload_msisdn(
            msisdn_list=msisdn_list,
            uploaded_by=uploaded_by,
            invalid_format=invalid_format,
            duplicate_msisdn=duplicate_msisdn,
            trace_id=trace_id,
            client_ip=client_ip,
            source_endpoint=source_endpoint,
            streaming_service=streaming_service,
            account_service=account_service,
            account_id=account_id,
        )

    def add_upload_file_status(
        self,
        trace_id: UUID,
        field: str,
        source_endpoint: str,
        client_ip: str,
        account_service: AbstractAccountService,
        account_id: int | None = None,
        account_name: str | None = None,
        success_count: int | None = None,
        failure_count: int | None = None,
        uploaded_by: str | None = None,
        message: str | None = None,
        error_results: list[dict] | None = None,
    ) -> bool:
        uploaded_by = self.user.email
        logger.info(f"add_upload_file_status Account ID: {uploaded_by}")
        if is_distributor_client(self.user):
            logger.info("Distributor client")
            account_id = self.user.organization.account.id  # type: ignore
            logger.info(f"Distributor client account:{account_id}")
        return self.sim_service.add_upload_file_status(
            trace_id=trace_id,
            field=field,
            source_endpoint=source_endpoint,
            client_ip=client_ip,
            account_service=account_service,
            message=message,
            uploaded_by=uploaded_by,
            success_count=success_count,
            failure_count=failure_count,
            error_results=error_results,
            account_id=account_id,
            account_name=account_name,
        )

    def bulk_update_sim_card_details(
        self,
        total_records: int,
        sim_profile: model.SimProfile,
        msisdn_factor: model.MSISDNFactor,
        invalid_records: list[dict],
        duplicate_imsi: list[IMSI],
        duplicate_msisdn: list[model.MSISDN],
        valid_imsi_list: list[IMSI],
        valid_msisdn_list: list[model.MSISDN],
        streaming_service: AbstractKafkaAPI,
        account_service: AbstractAccountService,
        client_ip: str,
        trace_id: UUID,
        source_endpoint: str,
        valid_data: DataFrame,
        uploaded_by: str | None = None,
        account_id: int | None = None,
    ) -> model.BulkSimCardUpdateResult:
        uploaded_by = self.user.email
        if is_distributor_client(self.user):
            account_id = self.user.organization.account.id  # type: ignore
            response = self.sim_service.check_imsi_account(
                account_id=account_id,
                imsi=valid_imsi_list,
            )
            if not response:
                raise IMSINotAllocated(
                    "Some of the Imsis are not allocated to the requested account."
                )
        return self.sim_service.bulk_update_sim_card_details(
            total_records=total_records,
            sim_profile=sim_profile,
            msisdn_factor=msisdn_factor,
            invalid_records=invalid_records,
            duplicate_imsi=duplicate_imsi,
            duplicate_msisdn=duplicate_msisdn,
            valid_imsi_list=valid_imsi_list,
            valid_msisdn_list=valid_msisdn_list,
            streaming_service=streaming_service,
            account_service=account_service,
            client_ip=client_ip,
            trace_id=trace_id,
            source_endpoint=source_endpoint,
            valid_data=valid_data,
            uploaded_by=uploaded_by,
            account_id=account_id,
        )

    def update_sim_card_details(
        self,
        sim_profile: model.SimProfile,
        total_records: int,
        client_ip: str,
        msisdn_factor: model.MSISDNFactor,
        streaming_service: AbstractKafkaAPI,
        account_service: AbstractAccountService,
        trace_id: UUID,
        source_endpoint: str,
        uploaded_by: str | None = None,
        account_id: int | None = None,
        imsi_list: list[IMSI] = [],
        msisdn_list: list[MSISDN] = [],
        duplicate_imsi: list[IMSI] = [],
        duplicate_msisdn: list[msisdn] = [],
        valid_data: list[dict] = [],
        invalid_data: list[dict] = [],
    ):
        uploaded_by = self.user.email
        if is_distributor_client(self.user):
            account_id = self.user.organization.account.id  # type: ignore
            response = self.sim_service.check_imsi_account(
                account_id=account_id,
                imsi=imsi_list,
            )
            if not response:
                raise IMSINotAllocated(
                    "Some of the Imsis are not allocated to the requested account."
                )
        return self.sim_service.update_sim_card_details(
            imsi_list=imsi_list,
            sim_profile=sim_profile,
            total_records=total_records,
            client_ip=client_ip,
            uploaded_by=uploaded_by,
            msisdn_list=msisdn_list,
            msisdn_factor=msisdn_factor,
            streaming_service=streaming_service,
            account_service=account_service,
            trace_id=trace_id,
            source_endpoint=source_endpoint,
            duplicate_imsi=duplicate_imsi,
            duplicate_msisdn=duplicate_msisdn,
            valid_data=valid_data,
            invalid_data=invalid_data,
            account_id=account_id,
        )

    def validate_common_request(self, msisdn_list: list[MSISDN]) -> bool:
        return self.sim_service.validate_common_request(msisdn_list=msisdn_list)

    def unallocate_sim_cards(
        self, imsi_list: list[IMSI]
    ) -> model.UnallocateSimCardDetails:
        if is_distributor_staff(self.user):
            return self.sim_service.unallocate_sim_cards(imsi_list=imsi_list)
        else:
            raise ForbiddenError

    def imsis_to_delete(self, imsi_list: list[IMSI]) -> model.IMSIDeleteResponse:

        if is_distributor_staff(self.user):
            return self.sim_service.imsis_to_delete(imsi_list=imsi_list)
        else:
            raise ForbiddenError

    def flush_sim(
        self,
        imsi: IMSI,
        created_by: str,
        client_ip: str | None = None,
    ):
        created_by = self.user.email
        if is_distributor_staff(self.user):
            return self.sim_service.flush_sim(imsi, created_by, client_ip)
        elif is_distributor_client(self.user):
            response = self.sim_service.check_imsi_account(
                account_id=self.user.organization.account.id,  # type: ignore
                imsi=[imsi],
            )
            if not response:
                logger.error(
                    f"Requested imsi {imsi} not allocated to the requested account."
                )
                raise IMSINotAllocated("Invalid IMSI.")
            return self.sim_service.flush_sim(imsi, created_by, client_ip)

    def pod_sim(
        self,
        imsi: IMSI,
        created_by: str,
        client_ip: str | None = None,
    ):
        created_by = self.user.email
        if is_distributor_staff(self.user):
            return self.sim_service.pod_sim(imsi, created_by, client_ip)
        elif is_distributor_client(self.user):
            response = self.sim_service.check_imsi_account(
                account_id=self.user.organization.account.id,  # type: ignore
                imsi=[imsi],
            )
            if not response:
                logger.error(
                    f"Requested imsi {imsi} not allocated to the requested account."
                )
                raise IMSINotAllocated("Invalid IMSI.")
            return self.sim_service.pod_sim(imsi, created_by, client_ip)

    def sms_sim(
        self,
        imsi: IMSI,
        message: str,
        created_by: str,
        client_ip: str | None = None,
    ):
        created_by = self.user.email
        if is_distributor_staff(self.user):
            return self.sim_service.sms_sim(imsi, message, created_by, client_ip)
        elif is_distributor_client(self.user):
            response = self.sim_service.check_imsi_account(
                account_id=self.user.organization.account.id,  # type: ignore
                imsi=[imsi],
            )
            if not response:
                logger.error(
                    f"Requested imsi {imsi} not allocated to the requested account."
                )
                raise IMSINotAllocated("Invalid IMSI.")
            return self.sim_service.sms_sim(imsi, message, created_by, client_ip)

    def apn_sim(
        self,
        imsi: IMSI,
        apn_id: str,
        created_by: str,
        sim_apn_action: model.SIMAPNAction,
        client_ip: str | None = None,
    ):
        created_by = self.user.email
        if is_distributor_staff(self.user):
            return self.sim_service.apn_sim(
                imsi,
                apn_id,
                created_by,
                sim_apn_action,
                client_ip,
            )
        elif is_distributor_client(self.user):
            response = self.sim_service.check_imsi_account(
                account_id=self.user.organization.account.id,  # type: ignore
                imsi=[imsi],
            )
            if not response:
                logger.error(
                    f"Requested imsi {imsi} not allocated to the requested account."
                )
                raise IMSINotAllocated("Invalid IMSI.")
            return self.sim_service.apn_sim(
                imsi,
                apn_id,
                created_by,
                sim_apn_action,
                client_ip,
            )

    def get_latest_data_session(
        self,
        imsi: IMSI,
    ) -> model.DataSession:
        """Get latest data session for a SIM by IMSI"""
        if is_distributor_staff(self.user):
            return self.sim_service.get_latest_data_session(imsi)

        elif is_distributor_client(self.user):

            response = self.sim_service.check_imsi_account(
                account_id=self.user.organization.account.id,  # type: ignore
                imsi=[imsi],
            )
            if not response:
                logger.error(
                    f"Requested imsi " f"{imsi} not allocated to the requested account."
                )
                raise IMSINotAllocated("Invalid IMSI.")
            return self.sim_service.get_latest_data_session(imsi)
        else:
            raise ForbiddenError

    def get_location(
        self,
        imsi: IMSI,
        pagination: Pagination | None = None,
    ) -> model.LocationResponse:
        """Get location data for a SIM by IMSI"""
        if is_distributor_staff(self.user):
            return self.sim_service.get_location(
                imsi=imsi,
                pagination=pagination,
            )

        elif is_distributor_client(self.user):

            response = self.sim_service.check_imsi_account(
                account_id=self.user.organization.account.id,  # type: ignore
                imsi=[imsi],
            )
            if not response:
                logger.error(
                    f"Requested imsi " f"{imsi} not allocated to the requested account."
                )
                raise IMSINotAllocated("Invalid IMSI.")
            return self.sim_service.get_location(
                imsi=imsi,
                pagination=pagination,
            )
        else:
            raise ForbiddenError

    def get_latest_location(
        self,
        imsi: IMSI,
    ) -> model.LocationItem:
        """Get latest location data for a SIM by IMSI"""
        if is_distributor_staff(self.user):
            return self.sim_service.get_latest_location(imsi)

        elif is_distributor_client(self.user):

            response = self.sim_service.check_imsi_account(
                account_id=self.user.organization.account.id,  # type: ignore
                imsi=[imsi],
            )
            if not response:
                logger.error(
                    f"Requested imsi " f"{imsi} not allocated to the requested account."
                )
                raise IMSINotAllocated("Invalid IMSI.")
            return self.sim_service.get_latest_location(imsi)
        else:
            raise ForbiddenError

    def get_cell_location(
        self,
        imsi: IMSI,
    ) -> model.CellLocation:
        """Get cell location data from external API"""
        return self.sim_service.get_cell_location(imsi)

    def worldov_token(self) -> model.TokenResponse:
        return self.sim_service.worldov_token()

    def worldov_token_refresh(
        self, refresh_token: model.WorldOVTokenRefresh
    ) -> model.TokenResponse:
        return self.sim_service.worldov_token_refresh(refresh_token)

    def get_sim_card_by_iccid(
        self,
        iccid: ICCID,
    ) -> model.IMSIDetails:
        return self.sim_service.get_sim_card_by_iccid(iccid=iccid)

    def locate_subscriber(
        self,
        imsi: IMSI,
        client_ip: str | None = None,
    ) -> model.LocateSubscriber:

        if is_distributor_staff(self.user):
            return self.sim_service.locate_subscriber(imsi=imsi, client_ip=client_ip)

        elif is_distributor_client(self.user):

            response = self.sim_service.check_imsi_account(
                account_id=self.user.organization.account.id,  # type: ignore
                imsi=[imsi],
            )
            if not response:
                logger.error(
                    f"Requested imsi " f"{imsi} not allocated to the requested account."
                )
                raise IMSINotAllocated("Invalid IMSI.")
            return self.sim_service.locate_subscriber(
                imsi=imsi,
                client_ip=client_ip,
            )
        else:
            raise ForbiddenError
