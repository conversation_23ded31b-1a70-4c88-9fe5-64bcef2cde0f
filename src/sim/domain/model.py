from dataclasses import InitVar, field
from datetime import date, datetime
from enum import Enum
from uuid import UUID

from pydantic import ConstrainedStr, Field, validate_arguments
from pydantic.dataclasses import dataclass

from common.types import ICCID as Schema_ICCID
from common.types import IMSI, Carrier, DigitalStr, FormFactor, Month, ServiceUsage
from sim.exceptions import InsufficientIMSI, RangeNotReady


class SimProfile(str, Enum):
    DATA_ONLY = "DATA_ONLY"
    VOICE_SMS_DATA = "VOICE_SMS_DATA"


class MSISDNFactor(str, Enum):
    NATIONAL = "NATIONAL"
    INTERNATIONAL = "INTERNATIONAL"


class ICCID(DigitalStr):
    min_length = 19
    max_length = 20


class MSISDN(DigitalStr):
    min_length = 8
    max_length = 15


class PIN(DigitalStr):
    min_length = 4
    max_length = 8


class PUK(DigitalStr):
    min_length = 8
    max_length = 8


class EID(DigitalStr):
    min_length = 32
    max_length = 32


@dataclass
class RatePlanInfo:
    account_id: int
    allocated_sim_count: int
    sim_limit: int | None


@dataclass
class Allocation:
    title: str
    account_id: int
    range_id: int
    imsi_last: IMSI | None = None
    imsi_first: IMSI | None = None
    imsi: IMSI | None = None
    iccid: ICCID | None = None
    msisdn: MSISDN | None = None
    id: int | None = None
    rate_plan_id: int | None = None
    created_at: datetime | None = None
    quantity: int | None = None
    form_factor: FormFactor | None = None
    created_by: str | None = None

    @property
    def sim_cards(self) -> list["SIMCard"]:
        return self._sim_cards

    @sim_cards.setter
    def sim_cards(self, sim_cards: list["SIMCard"]) -> None:
        self._sim_cards = sim_cards


class SimStatus(str, Enum):
    READY_FOR_ACTIVATION = "Ready for Activation"
    ACTIVE = "Active"
    DEACTIVATED = "Deactivated"
    PENDING = "Pending"


class SIMAPNAction(str, Enum):
    ADD = "add"
    REMOVE = "remove"
    MAKEDEFAULT = "makedefault"


class SIMCard:
    _allocation: Allocation | None = None

    @validate_arguments
    def __init__(
        self,
        iccid: ICCID,
        msisdn: MSISDN,
        imsi: IMSI,
        range_id: int | None = None,
        form_factor: FormFactor | None = None,
        id: int | None = None,
        allocation_id: int | None = None,
        rate_plan_id: int | None = None,
        pin_1: PIN | None = None,
        pin_2: PIN | None = None,
        puk_1: PUK | None = None,
        puk_2: PUK | None = None,
        sim_status: SimStatus | None = None,
        sim_profile: SimProfile | None = None,
        sim_eid: EID | None = None,
    ) -> None:
        self.iccid = iccid
        self.msisdn = msisdn
        self.imsi = imsi
        self.range_id = range_id
        self.form_factor = form_factor
        self.id = id
        self.allocation_id = allocation_id
        self.rate_plan_id = rate_plan_id
        self.pin_1 = pin_1
        self.pin_2 = pin_2
        self.puk_1 = puk_1
        self.puk_2 = puk_2
        self.sim_status = sim_status
        self.sim_profile = sim_profile
        self.sim_eid = sim_eid

        self.usages: list[ServiceUsage] = []

    @property
    def allocation(self) -> Allocation | None:
        return self._allocation

    @allocation.setter
    def allocation(self, allocation: Allocation | None) -> None:
        self._allocation = allocation


class Range:
    _allocations: list[Allocation]
    _sim_cards: list[SIMCard]

    def __init__(
        self,
        title: str,
        form_factor: FormFactor,
        id: int = None,
        created_at: datetime = None,
        imsi_first: IMSI = None,
        imsi_last: IMSI = None,
        remaining: int = 0,
        quantity: int = 0,
        created_by: str = None,
    ) -> None:
        self.id = id
        self.title = title
        self.form_factor = form_factor
        self.created_at = created_at
        self.created_by = created_by
        self.imsi_first = imsi_first
        self.imsi_last = imsi_last
        if (imsi_first and imsi_last) and not quantity:
            self.remaining = self.quantity = self._calculate_imsi_range()
        else:
            self.remaining = remaining
            self.quantity = quantity

    def _calculate_imsi_range(self) -> int:
        if self.imsi_first and self.imsi_last:
            return int(self.imsi_last) - int(self.imsi_first) + 1
        return 0

    @property
    def provider(self) -> str:
        return "NR"

    @property
    def sim_cards(self) -> list[SIMCard]:
        return self._sim_cards

    @sim_cards.setter
    def sim_cards(self, sim_cards: list[SIMCard]) -> None:
        self._sim_cards = sim_cards

    @property
    def allocations(self) -> list[Allocation]:
        return self._allocations

    @allocations.setter
    def allocations(self, allocation: list[Allocation]) -> None:
        self._allocations = allocation

    def _check_if_allocation_possible(self, allocation: Allocation) -> None:
        if self.imsi_first is None or self.imsi_last is None:
            raise RangeNotReady("Range is not ready for allocations yet.")
        if self.remaining < allocation.quantity:  # type: ignore
            raise InsufficientIMSI(
                (
                    f"Not enough IMSI for allocation. "
                    f"Remaining: {self.remaining}, "
                    f"requested: {allocation.quantity}."
                )
            )

    def allocate(self, allocation: Allocation) -> Allocation:
        self._check_if_allocation_possible(allocation)
        if self.imsi_last is None:
            raise AssertionError("imsi_last must be set before allocate operation")
        imsi_first = int(self.imsi_last) - self.remaining + 1
        imsi_last = imsi_first + allocation.quantity - 1  # type: ignore
        allocation.imsi_first = IMSI(imsi_first)  # type: ignore
        allocation.imsi_last = IMSI(imsi_last)  # type: ignore
        allocation.created_at = datetime.utcnow()
        self.remaining = self.remaining - allocation.quantity  # type: ignore
        self.allocations.append(allocation)
        return allocation


class RequestType(str, Enum):
    CEASE = "Cease"
    PROVIDE = "Provide"
    INVALID = "Invalid"
    FLUSH = "Flush"
    APN = "APN"
    POD = "POD"
    SMS = "SMS"


class NotificationStatus(str, Enum):
    SUCCESS = "SUCCESS"
    FAILURE = "FAILURE"
    DELAYED = "DELAYED"
    FAILED = "FAILED"


@dataclass
class SIMProviderLog:
    sim_activity_log_uuid: str
    activity_id: str
    audit_date: datetime
    message: str
    status: str
    work_id: str
    prior_status: SimStatus


@dataclass
class SIMMonthlyStatus:
    sim_card_id: int
    month: Month
    sim_status: SimStatus
    is_first_activation: bool
    _id: InitVar[int | None] = None

    def __post_init__(self, _id: int | None):
        if _id is not None:
            self.sim_card_id = _id


@dataclass
class SIMStatusResponse:
    reference_id: int
    imsi: IMSI
    msisdn: MSISDN
    sim_status: str


@dataclass
class SIMActivateResponse:
    uuid: str
    message: str
    status: str


@dataclass
class SIMDeactivatedResponse:
    uuid: str
    message: str
    status: str


@dataclass
class AuditLogs:
    field: str
    prior_value: SimStatus
    new_value: str
    date: datetime
    user_name: str


@dataclass
class ConnectionSummary:
    msisdn: MSISDN
    imsi: IMSI
    iccid: ICCID
    first_activated: date | None
    last_session: datetime | None
    sim_status: SimStatus
    rate_plan_id: int
    rate_plan: str | None


@dataclass
class SimUsage:
    sim_id: int
    iccid: ICCID
    msisdn: MSISDN
    imsi: IMSI
    eid: EID | None
    type: FormFactor
    allocation_reference: str
    allocation_date: datetime | None
    sim_status: SimStatus
    usage: int
    rate_plan: str
    ee_usage: int
    sim_profile: SimProfile
    msisdn_factor: MSISDNFactor


@dataclass
class ActiveSimMonthlyStatistic:
    id: int
    imsi: IMSI
    iccid: ICCID
    msisdn: MSISDN
    sim_status: SimStatus
    is_first_activation: bool
    usage: int
    rate_plan_id: int
    allocation_id: int | None = None
    created_at: datetime | None = None

    def __iter__(self):
        return iter(self)


@dataclass
class SimCDRHistory:
    iccid: ICCID
    country: str
    carrier: Carrier
    session_starttime: datetime
    session_endtime: datetime
    duration: int
    data_volume: int
    imsi: IMSI
    country_name: str
    carrier_name: str


@dataclass
class MonthUsage:
    total_usage: int
    total_sims: int
    total_active_sims: int
    total_deactivated_sims: int
    total_pending_sims: int
    total_ready_activation_sims: int


@dataclass
class SimStatusDetails:
    iccid: ICCID
    imsi: IMSI
    msisdn: MSISDN
    sim_status: SimStatus


@dataclass
class SIMWorkItemStatusResponse:
    reference: str
    audit_date: datetime
    request_type: RequestType
    status: NotificationStatus
    message: str


@dataclass
class sim_card_msisdn:
    iccid: ICCID
    imsi: IMSI
    msisdn: MSISDN


@dataclass
class SIMActivityLog:
    imsi: IMSI
    iccid: Schema_ICCID
    msisdn: MSISDN
    request_type: str
    prior_value: SimStatus
    new_value: SimStatus
    created_by: str
    uuid: UUID
    client_ip: str | None = None
    _uuid: InitVar[UUID | None] = None

    def __post_init__(self, _uuid: UUID | None):
        if _uuid is not None:
            self.uuid = _uuid


@dataclass
class IMSIDetails:
    iccid: Schema_ICCID
    imsi: IMSI


@dataclass
class MarketShareIMSI:
    imsi: IMSI


@dataclass
class MarketShareUsage:
    carrier: Carrier
    usage: int


@dataclass
class MarketShareCarrier:
    totalUsage: int
    summary: list[MarketShareUsage]


@dataclass
class MarketShareModel:
    imsis: list[IMSI] = Field(min_items=1)
    from_date: date | None = None
    to_date: date | None = None


@dataclass
class MarketSharePeriod:
    from_date: date | None = None
    to_date: date | None = None


@dataclass
class MarketShareData:
    account_id: int
    from_date: date | None = None
    to_date: date | None = None


@dataclass
class CarrierName:
    carrier: Carrier
    carrier_name: str | None = None


# EE_usage Class
@dataclass
class MarketShareIMSIData:
    imsi: IMSI
    usageSummary: list[MarketShareUsage]


# EE_usage Class
@dataclass
class MarketShare:
    total_usage: int
    summary: list[MarketShareIMSIData]


@dataclass
class SimVoiceCDRHistory:
    iccid: ICCID
    imsi: IMSI
    country: str
    carrier: Carrier
    call_date: datetime
    call_number: str
    call_minutes: int
    country_name: str
    carrier_name: str


@dataclass
class SimSMSCDRHistory:
    iccid: ICCID
    imsi: IMSI
    country: str
    carrier: Carrier
    date_sent: datetime
    sent_from: str
    sent_to: str
    country_name: str
    carrier_name: str


@dataclass
class CustomModel:
    imsi: IMSI
    form_factor: FormFactor
    allocation_id: int | None = None
    account_name: str | None = None


@dataclass
class AllocationDetails:
    file_name: str
    total_sim: int
    error_sim: int
    id: int | None = None


@dataclass
class AllocationSummary:
    id: int
    title: str
    account_id: int
    account_name: str
    quantity: int
    created_at: datetime
    form_factor: FormFactor
    provider: str
    logo_key: str | None = None
    logo_url: str | None = None
    country: str | None = None


@dataclass
class AllocationResult:
    totalSIM: int
    errorSIM: int
    results: list[dict] | None = None


@dataclass
class SIMCardAudit:
    uuid: UUID
    imsi: IMSI
    iccid: Schema_ICCID
    msisdn: MSISDN
    request_type: str
    prior_value: str | None
    new_value: str | None
    field: str
    action: str
    client_ip: str | None = None
    created_by: str | None = None
    prior_rate_plan: int | None = None
    new_rate_plan: int | None = None
    allocation_date: datetime | None = None
    audit_date: datetime | None = None
    message: str | None = None
    status: str | None = None
    work_id: str | None = None
    prior_status: str | None = None


@dataclass
class SIMCardProviderAudit:
    sim_activity_log_uuid: UUID
    activity_id: str
    audit_date: datetime
    message: str
    status: str
    work_id: str
    prior_status: str


@dataclass
class Providerlog:
    activity_id: str
    imsi: IMSI
    iccid: ICCID
    msisdn: MSISDN
    request_type: str
    audit_date: str
    message: str
    status: str
    work_id: str
    prior_status: str


@dataclass
class SIMPendinglog:
    work_id: str


@dataclass
class SIMCardAuditLogs:
    id: str
    imsi: IMSI
    request_type: str
    field: str
    action: str
    iccid: str | None
    msisdn: str | None
    prior_value: str | None
    new_value: str | None
    client_ip: str | None = None
    created_by: str | None = None
    created_date: str | None = None


@dataclass
class ReAllocationResult:
    validSIM: int
    errorSIM: int
    valid_imsi_list: list[IMSI]
    same_account_imsi_list: list[IMSI] | None = None
    message: str | None = None


@dataclass
class RatePlanChangeSimLimitResult:
    message: str


@dataclass
class MsisdnPool:
    msisdn: MSISDN
    sim_profile: SimProfile
    msisdn_factor: MSISDNFactor
    uploaded_by: str | None = None
    created_at: datetime | None = None
    allocation_id: int | None = None


@dataclass
class MsisdnDetails:
    msisdn: MSISDN
    sim_profile: SimProfile
    msisdn_factor: MSISDNFactor
    created_at: datetime
    uploaded_by: str
    imsi: IMSI | None = None
    country: str | None = None
    sim_provider: str | None = None
    account_name: str | None = None
    logo_key: str | None = None
    logo_url: str | None = None


@dataclass
class MsisdnCountDetails:
    total_count: int
    national: int
    international: int


@dataclass
class MsisdnResult:
    error_msisdn: int
    error_result: list


@dataclass
class MsisdnPoolAudit:
    uuid: UUID
    msisdn: MSISDN
    request_type: str
    prior_value: str | None
    new_value: str | None
    field: str
    action: str
    client_ip: str | None = None
    created_by: str | None = None


@dataclass
class UpdateSimCardDetailsResult:
    imsi: IMSI
    msisdn: MSISDN
    sim_profile: SimProfile


@dataclass
class UploadedFileStatus:
    source_endpoint: str
    message: str
    success_count: int
    failure_count: int
    created_at: datetime = field(default_factory=datetime.now)
    error_results: list[dict] | None = None


@dataclass
class UploadedFileAudit:
    request_id: UUID
    client_ip: str
    action: str
    field: str
    status: str
    payload: UploadedFileStatus
    user: str | None = None
    created_date: datetime | None = None
    account_id: int | None = None
    account_name: str | None = None


class SimHLRProfileProfile(str, Enum):
    OV_BTUK_iot_Data = "OV_BTUK_iot_Data"
    OV_BTUK_iot_Voice = "OV_BTUK_iot_Voice"


SimProfileDetails = {
    SimProfile.DATA_ONLY.name: SimHLRProfileProfile.OV_BTUK_iot_Data.value,
    SimProfile.VOICE_SMS_DATA.name: SimHLRProfileProfile.OV_BTUK_iot_Voice.value,
}


@dataclass
class BulkSimCardUpdateResult:
    total_details: int
    error_details: int
    error_results: list


@dataclass
class SimCardData:
    requested_imsi: IMSI
    requested_msisdn: MSISDN
    allocation_id: int | None = None
    existing_msisdn: MSISDN | None = None
    msisdn_value: MSISDN | None = None
    msisdn_sim_profile: SimProfile | None = None
    msisdn_factor: MSISDNFactor | None = None
    existing_imsi: IMSI | None = None


SimProfileData = {
    SimProfile.DATA_ONLY.name: SimProfile.DATA_ONLY,
    SimProfile.VOICE_SMS_DATA.name: SimProfile.VOICE_SMS_DATA,
}


@dataclass
class InvalidSimMsisdnCountDetails:
    duplicate_iccids: int
    duplicate_imsis: int
    duplicate_msisdns: int
    duplicate_msisdn_pool_msisdns: int
    sim_card_msisdn_missing_msisdn_pool: int
    sim_card_allocation_id_mismatch_msisdn_pool: int
    create_range_count: int
    add_msisdn_pool_count: int
    update_sim_msisdn_count: int | None = 0


@dataclass
class Unallocation:
    imsis: list[IMSI] = Field(min_items=1, max_items=100)


@dataclass
class ISMIToDelete:
    imsis: list[IMSI] = Field(min_items=1, max_items=100)


@dataclass
class UnallocateSimCardDetails:
    message: str


@dataclass
class IMSIDeleteResponse:
    message: str


@dataclass
class SIMEID:
    eid: EID | None = None
    sim_card_id: int | None = None


@dataclass
class AccountActivityLog:
    account_id: str
    account_name: str
    user: str
    prior_value: str
    new_value: str
    prior_text: str
    new_text: str
    field: str
    action: str
    client_ip: str


@dataclass
class AccountAuditReponse:
    id: str


@dataclass
class SIMFlushResponse:
    trid: str
    message: str
    imsi: IMSI
    msisdn: MSISDN


@dataclass
class SIMPODResponse:
    trid: str
    message: str
    imsi: IMSI
    msisdn: MSISDN


@dataclass
class QuerySIMActiveCallResponse:
    trid: str
    message: str
    call_id: str
    imsi: IMSI
    msisdn: MSISDN


@dataclass
class SIMSendSMSResponse:
    trid: str
    call_id: str
    imsi: IMSI
    msisdn: MSISDN
    message: str


@dataclass
class SIMAPNResponse:
    message: str
    status: str


class MANXResponseMessage(ConstrainedStr):
    min_length = 0
    max_length = 240


@dataclass
class SIMActionAudit:
    uuid: UUID
    imsi: IMSI
    iccid: Schema_ICCID
    msisdn: MSISDN
    request_type: str
    response: MANXResponseMessage
    field: str
    action: str
    client_ip: str | None = None
    created_by: str | None = None
    sim_ip: str | None = None
    message: MANXResponseMessage | None = None


@dataclass
class TokenResponse:
    authentication_token: str
    refresh_token: str | None = None
    expiry_time: str | None = None
    first_name: str | None = None
    last_name: str | None = None
    user_name: str | None = None
    email: str | None = None
    default_partner_id: int | None = None


@dataclass
class WorldOVTokenRefresh:
    authentication_token: str
    refresh_token: str


@dataclass
class SIMFetchMNCMCC:
    trid: str
    mcc: str
    mnc: str
    lac: str
    cell_id: str
    age: str
    source: str
    network_domain: str


@dataclass
class DataSession:
    id: str
    iccid: str
    imsi: str
    apn_name: str
    mobile_country_code: str
    mobile_network_code: str
    bytes_total: int
    bytes_mo: int
    bytes_mt: int
    bytes_limit: int
    bytes_limit_threshold: int
    bytes_limit_used: int
    partner_id: int
    start_time: str | None = None
    last_updated: str | None = None
    end_time: str | None = None
    ip_address: str | None = None
    lac: str | None = None
    cellId: str | None = None


@dataclass
class LocationItem:
    id: str | None
    imsi: str | None
    first_location_update: str | None
    last_location_update: str | None
    msc_global_title: str | None
    vlr_global_title: str | None
    sgsn_global_title: str | None
    dra_mobile_country_code: str | None
    dra_mobile_network_code: str | None
    network_name: str | None
    country_name: str | None
    continent_name: str | None
    country_flag: str | None


@dataclass
class LocationMeta:
    current_page: int
    total_pages: int
    items_per_page: int
    total_items: int


@dataclass
class LocationResponse:
    meta: LocationMeta
    items: list[LocationItem]


@dataclass
class CellLocation:
    mcc: str
    mnc: str
    lac: str
    cell: str
    lat: float
    lon: float
    range: int
    source: str
