"""Socket event handlers for SIM-related events."""

import logging
from typing import List

logger = logging.getLogger(__name__)


class SimSocketEvents:
    """
    Socket.IO event handlers for SIM-related events.
    """

    def __init__(self, sio):
        self.sio = sio
        self.register_events()

    def register_events(self) -> None:
        """Register all SIM-related event handlers."""
        pass

    async def sim_details_update(self, imsi_list: List, status: str) -> None:
        event_data = {"imsi": imsi_list, "simStatus": status}
        try:
            await self.sio.emit("sim_details_update", event_data)
            logger.info("[Socket] Emitted 'sim_details_update' event")
            logger.debug(f"[Socket] Payload: {event_data}")
        except Exception as e:
            logger.error(
                f"[Socket] Failed to emit 'sim_details_update' event: {str(e)}"
            )
            logger.debug(f"[Socket] Event data: {event_data}")

    async def sim_send_sms(
        self, imsi: str, response: str, message: str, created_by: str, created_at: str
    ) -> None:
        event_data = {
            "imsi": imsi,
            "message": message,
            "response": response,
            "created_by": created_by,
            "created_at": created_at,
        }
        try:
            await self.sio.emit("sim_send_sms", event_data)
            logger.info("[Socket] Emitted 'sim_send_sms' event")
            logger.debug(f"[Socket] Payload: {event_data}")
        except Exception as e:
            logger.error(f"[Socket] Failed to emit 'sim_send_sms' event: {str(e)}")
            logger.debug(f"[Socket] Event data: {event_data}")


def get_sim_socket_events(sio):
    """
    Create and return a SimSocketEvents instance.

    Args:
        sio: The Socket.IO server instance

    Returns:
        SimSocketEvents: The SIM socket events handler
    """
    return SimSocketEvents(sio)
