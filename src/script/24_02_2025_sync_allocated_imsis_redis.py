import logging

import requests
from fastapi import status
from fastapi.encoders import jsonable_encoder
from platform_api_client import PlatformAPIError
from sqlalchemy import func, select
from sqlalchemy.orm import Session

from app.config import settings
from app.db.session import make_session
from app.security import get_service_access_token
from auth.exceptions import ForbiddenError, Unauthorized
from redis.exception import RedisAPIError
from sim.adapters import orm


def get_db_session() -> Session:
    session = make_session()
    try:
        return session
    finally:
        session.close()


class AutomationScriptError(Exception):
    ...


class DatabaseRARepository:
    def __init__(self, session: Session) -> None:
        self.session = session
        self.condition_true = True

    def get_imsi_list_grouped(self) -> list:
        allocation = orm.sim_allocation

        query = (
            select(
                allocation.c.account_id,
                allocation.c.rate_plan_id,
                func.array_agg(allocation.c.imsi).label("imsi_list"),
            )
            .where(allocation.c.rate_plan_id.is_not(None))
            .group_by(allocation.c.account_id, allocation.c.rate_plan_id)
        )
        result = self.session.execute(query).all()
        return [
            {
                "account_id": row.account_id,
                "rate_plan_id": row.rate_plan_id,
                "imsi_list": row.imsi_list,
            }
            for row in result
        ]


class AuthService:
    def __init__(
        self,
    ):
        self.ALLOCATE_SIMS = (
            "/v1/analytics/imsis/account/{account_id}/rateplan/{rate_plan_id}"
        )

        self.token = get_service_access_token()
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.token}",
        }

    def sync_allocated_sims(self, account_id: int, rate_plan_id: int, data: dict):
        try:
            url = f"{settings.APP_BASE_URL}{self.ALLOCATE_SIMS.format(account_id=account_id, rate_plan_id=rate_plan_id)}"  # noqa
            logging.debug(f"Syncing IMSI allocation: {url}")

            response = requests.put(
                url=url,
                headers=self.headers,
                json=jsonable_encoder(data),
                timeout=settings.TIMEOUT,
            )
            if response.status_code == 202:
                return response.json()
            else:
                raise RedisAPIError
        except PlatformAPIError as e:
            logging.error(f"Error in sync_allocated_sims. Error: {str(e)}")
            match e.status_code:
                case status.HTTP_401_UNAUTHORIZED:
                    raise Unauthorized("Unauthorized access.")
                case status.HTTP_403_FORBIDDEN:
                    raise ForbiddenError()
                case _:
                    logging.error(f"Error in sync_allocated_sims. Error: {str(e)}")
        except ConnectionError:
            raise ConnectionError("Failed to connect with the Redis API")
        except requests.exceptions.Timeout as e:
            logging.error(
                f"Request timed out while syncing allocated sims in redis : {str(e)}"
            )
            raise AutomationScriptError("Request timeout occurred.")


if __name__ == "__main__":
    session = get_db_session()
    repository = DatabaseRARepository(session)
    auth_service = AuthService()
    try:
        results = repository.get_imsi_list_grouped()
        for data in results:
            logging.info(f"Data: {data}")
            account_id = data["account_id"]
            rate_plan_id = data["rate_plan_id"]
            data = {"IMSIs": data["imsi_list"]}
            auth_service.sync_allocated_sims(account_id, rate_plan_id, data)

        logging.info("IMSIs synced successfully in redis.")
    except AutomationScriptError as e:
        logging.error(f"Error in syncing SIM in redis. Error: {str(e)}")
        raise e

"""python -m script.24_02_2025_sync_allocated_imsis_redis"""
