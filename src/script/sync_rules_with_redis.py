import requests
from fastapi import status
from fastapi.encoders import jsonable_encoder
from platform_api_client import PlatformAPIError
from sqlalchemy import select
from sqlalchemy.orm import Session

from app.config import logger, settings
from app.db.session import make_session
from app.security import get_service_access_token
from auth.exceptions import ForbiddenError, Unauthorized
from automation.adapters import orm as automation_rule
from common.funcs import convert_to_bytes
from redis.adapters.repository import DatabaseRepository
from redis.domain.model import RuleInfo
from redis.exception import RedisAPIError


def get_db_session() -> Session:
    session = make_session()
    try:
        return session
    finally:
        session.close()


class AutomationScriptError(Exception):
    ...


class DatabaseRARepository:
    def __init__(self, session: Session) -> None:
        self.session = session
        self.condition_true = True

    def get_account_ids(self) -> list:
        rule = automation_rule.rules
        TRUE = True
        query = query = select(rule.c.account_id).where(
            rule.c.status == TRUE,
        )
        rules = self.session.execute(query).all()
        return [rule.account_id for rule in rules]


class AuthService:
    def __init__(
        self,
    ):
        self.CREATE_REDIS_RULE = "/v1/analytics/rule"

        self.token = get_service_access_token()
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.token}",
        }

    def create_redis_rule(self, rule: RuleInfo):
        try:
            url = f"{settings.APP_BASE_URL}{self.CREATE_REDIS_RULE}"
            logger.debug(f"Creating redis rule url: {url}")
            response = requests.post(
                url=url,
                headers=self.headers,
                json=jsonable_encoder(rule),
                timeout=settings.TIMEOUT,
            )
            if response.status_code == 201:
                return response.json()
            else:
                raise RedisAPIError
        except PlatformAPIError as e:
            logger.error(f"Error in create_redis_rule. Error: {str(e)}")
            match e.status_code:
                case status.HTTP_401_UNAUTHORIZED:
                    raise Unauthorized("Unauthorized access.")
                case status.HTTP_403_FORBIDDEN:
                    raise ForbiddenError()
                case _:
                    logger.error(f"Error in create_redis_rule. Error: {str(e)}")
        except ConnectionError:
            raise ConnectionError("Failed to connect with the Redis API")
        except requests.exceptions.Timeout as e:
            logger.error(f"Request timed out while creating Redis rule: {str(e)}")
            raise AutomationScriptError("Request timeout occurred.")


if __name__ == "__main__":
    session = get_db_session()
    account_rule_repo = DatabaseRARepository(session)
    repository = DatabaseRepository(session)
    auth_service = AuthService()
    try:
        account_ids = account_rule_repo.get_account_ids()
        account_ids = list(set(account_ids))
        for account_id in account_ids:
            rules_data = repository.get_rules(account_id=account_id)
            for rule_data in rules_data.rules:  # type: ignore
                rule_data.sizeInBytes = convert_to_bytes(
                    rule_data.simUsageLimit, rule_data.unit  # type: ignore
                )
            auth_service.create_redis_rule(rules_data)  # type: ignore

        logger.info("Rules created successfully in redis.")
    except AutomationScriptError as e:
        logger.error(f"Error in creating rules in redis. Error: {str(e)}")
        raise e

"""python -m script.sync_rules_with_redis"""
