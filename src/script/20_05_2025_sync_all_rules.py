import logging
import textwrap
from uuid import UUID

import requests
from fastapi.encoders import jsonable_encoder
from sqlalchemy import and_, select
from sqlalchemy.orm import Session

from api.rate_plans.schemas import RatePlanViewDetails  # noqa: F401
from app.config import settings
from app.db.session import make_session
from app.security import get_service_access_token
from automation.adapters import orm
from common.funcs import convert_to_bytes
from common.types import Unit
from redis.adapters.repository import DatabaseRepository
from redis.domain import model
from redis.exception import RedisAPIError

TRUE = True


def get_db_session() -> Session:
    session = make_session()
    try:
        return session
    finally:
        session.close()


class AutomationScriptError(Exception):
    ...


class DatabaseRARepository:
    def __init__(self, session: Session) -> None:
        self.session = session
        self.condition_true = True

    def get_active_rules_acc_ids(self) -> list[int]:
        rules = orm.rules
        stmt = select(rules.c.account_id).where(
            and_(
                rules.c.status == TRUE,
            )
        )
        result = self.session.execute(stmt).scalars().all()
        if not result:
            raise Exception("No active rules found")
        return result


class AuthService:
    def __init__(
        self,
    ):
        self.CREATE_REDIS_RULE = "/v1/analytics/rule"

        self.token = get_service_access_token()
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.token}",
        }
        self.session = get_db_session()
        self.repository = DatabaseRepository(self.session)

    def create_redis_rules(self, account_id: list[int], rule_uuid: UUID | None = None):
        for acc_id in account_id:
            if not isinstance(acc_id, int):
                raise TypeError(
                    f"Expected account_id to be of type int, but got {type(acc_id)}"
                )
            rules_data = self.repository.get_rules(acc_id, rule_uuid=rule_uuid)
            if not isinstance(rules_data, model.RuleInfo):
                raise TypeError("Expected rules_data to be of type RuleInfo")
            for rule_data in rules_data.rules:
                if not isinstance(rule_data.simUsageLimit, int):
                    raise TypeError(
                        textwrap.dedent(
                            f"""
                        Expected simUsageLimit to be of type int,
                        but got {type(rule_data.simUsageLimit)}
                    """
                        ).strip()
                    )
                if not isinstance(rule_data.unit, Unit):
                    raise TypeError("Expected rule_data.unit to be of type Unit")
                if rule_data.unit != Unit.PERCENTAGE.value:
                    rule_data.sizeInBytes = convert_to_bytes(
                        rule_data.simUsageLimit, rule_data.unit
                    )
                else:
                    rule_data.sizeInBytes = rule_data.simUsageLimit
            self.create_redis_rule(rules_data)

    def create_redis_rule(self, rule: model.RuleInfo):
        try:
            url = f"{settings.APP_BASE_URL}{self.CREATE_REDIS_RULE}"
            logging.debug(f"Creating redis rule url: {url}")
            response = requests.post(
                url,
                headers=self.headers,
                json=jsonable_encoder(rule),
                timeout=settings.TIMEOUT,
            )
            if response.status_code == 201:
                return response.json()
            else:
                raise RedisAPIError
        except Exception as e:
            logging.error(f"Error in create_redis_rule. Error: {str(e)}")
            raise
        except ConnectionError:
            raise ConnectionError("Failed to connect with the Redis API")


if __name__ == "__main__":
    session = get_db_session()
    repository = DatabaseRARepository(session)
    auth_service = AuthService()

    try:
        ids = repository.get_active_rules_acc_ids()
        auth_service.create_redis_rules(ids)
        logging.info("Fixed Flexi plans synced successfully in redis.")
    except AutomationScriptError as e:
        logging.error(f"Error in syncing Fixed Flexi plans in redis. Error: {str(e)}")
        raise e

"""python -m script.20_05_2025_sync_all_rules"""
