DATABASE_URI=postgresql://monoglass_user:monoglass_password@localhost/monoglass
PIP_URI=https://bt.s.im/pip/api/execute.mth 
PIP_USERNAME=BT_PPL 
PIP_PASSWORD=*********
PPL_URI=https://pplportal.manxtelecom.im/ 
PPL_CUSTOMER_STATUS=api/provisioning/query 
PPL_CUSTOMER_STATUS_HLR=api/manxprovisioning/hlrquery 
PPL_SIM_ACTIVATE=api/provisioning/provide 
PPL_SIM_SUSPEND=api/provisioning/cease 
PPL_REGISTER=api/provisioning/URL
PPL_REGISTER_UPDATE=api/provisioning/updateurl 
PPL_USERNAME=<EMAIL> 
PPL_PASSWORD=*********
S3_REGION_NAME=ap-northeast-1 
S3_ACCESS_KEY_ID=*********
S3_SECRET_ACCESS_KEY=*********
S3_ENDPOINT_URL=http://cdrpush.s3.amazonaws.com/ 
S3_CDR_BUCKET_NAME=cdrpush 
S3_CDR_BUCKET_PREFIX=monthly
USE_S3_STORAGE=True 
SPOG_NOTIFICATION_URI=https://dev.spogconnected.com/v1/glass/sim/cards/notifications
LANDING_PAGE_URL=https://dev.spogconnected.com # used as a redirect url after successful user registration in SAF
DIGITAL_IDENTITY_URL=http://localhost/digital-identity
DIGITAL_IDENTITY_CLIENT_ID=digital-identity
DIGITAL_IDENTITY_CLIENT_SECRET=digital-identity-secret
WHITELISTED_IPS=***********/24, ***********/24
QUEUE_NAME=sns-sqs-dev01-env01-SAF-IOTNG
ACCESS_KEY_ID=*********
SECRET_KEY=fpLsnBpZ3dc0WSLKMnA8998E6Tt1KdmmRZEqtN68
REGION_NAME=eu-west-2
EXECUTE_TIME=10
SQS_QUEUE_WAIT_TIME=10
MAX_MESSAGES_LIMIT=5
S3_CUSTOM_ALLOCATION_BUCKET_NAME=customallocation
SAF_EVENT_URL=https://dev.spogconnected.com/v1/glass/accounts/saf-event
SQS_URL=https://sqs.eu-west-2.amazonaws.com/************/sns-sqs-dev01-env01-SAF-IOTNG

ADD_MONTHLY_DATA=alembic/data/add_monthly_data.csv
SERVICE_ACCOUNT_PASSWORD=""
ORGANIZATIONS_URL=http://dev.spogconnected.com

PIP_SMS_ORIGIN=************
PIP_SMS_LENGTH=240
